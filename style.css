/* ===== المتغيرات العامة ===== */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196F3;
    
    /* ألوان الخلفية */
    --bg-primary: #f0f2f5;
    --bg-secondary: #ffffff;
    --bg-tertiary: #e8ecf0;
    
    /* ألوان النص */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    
    /* الظلال */
    --shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --shadow-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    --shadow-hover: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff;
    
    /* الخطوط */
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الانتقالات */
    --transition: all 0.3s ease;
}

/* الثيم الداكن */
[data-theme="dark"] {
    --bg-primary: #1a1a2e;
    --bg-secondary: #16213e;
    --bg-tertiary: #0f3460;
    --text-primary: #eee;
    --text-secondary: #bbb;
    --text-light: #888;
    --shadow-light: 8px 8px 16px #0d1117, -8px -8px 16px #252d3a;
    --shadow-inset: inset 8px 8px 16px #0d1117, inset -8px -8px 16px #252d3a;
    --shadow-hover: 4px 4px 8px #0d1117, -4px -4px 8px #252d3a;
}

/* ===== الإعدادات العامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* ===== الفئات المساعدة ===== */
.hidden {
    display: none !important;
}

.loading {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* ===== شاشة تسجيل الدخول ===== */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.login-container {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header .logo i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.login-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.login-form {
    text-align: right;
}

.input-group {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.input-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.input-group input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: 15px;
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.input-group input:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    margin-top: 1.2rem;
}

.login-btn {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-lg);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.login-btn:active {
    transform: translateY(0);
}

/* Login info section removed for security reasons */

/* رسالة خطأ تسجيل الدخول */
.login-error {
    background: var(--error-color);
    color: white;
    padding: var(--spacing-sm);
    border-radius: 8px;
    margin-top: var(--spacing-md);
    text-align: center;
    font-size: var(--font-size-sm);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* ===== إدارة المستخدمين ===== */
.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.user-card {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.user-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.username {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-family: 'Courier New', monospace;
}

.user-role {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.user-role.admin {
    background: var(--primary-color);
    color: white;
}

.user-role.manager {
    background: var(--warning-color);
    color: white;
}

.user-role.cashier {
    background: var(--info-color);
    color: white;
}

.user-role.viewer {
    background: var(--text-secondary);
    color: white;
}

.user-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.user-status.active {
    background: var(--success-color);
    color: white;
}

.user-status.inactive {
    background: var(--error-color);
    color: white;
}

.user-details {
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.detail-item .value {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.user-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    min-width: auto;
}

@media (max-width: 768px) {
    .users-grid {
        grid-template-columns: 1fr;
    }

    .user-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .user-actions {
        justify-content: flex-start;
    }
}

/* ===== إدارة صور المنتجات ===== */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.image-preview {
    position: relative;
    width: 150px;
    height: 150px;
    border: 2px dashed var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    background: var(--bg-secondary);
}

.image-preview:hover {
    border-color: var(--primary-color);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.2);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.image-preview:hover .image-overlay {
    opacity: 1;
}

.image-overlay i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.image-upload-container input[type="file"] {
    display: none;
}

.image-info {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.image-preview.loading {
    border-color: var(--primary-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* صور المنتجات في السلة */
.cart-item-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.cart-item-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.cart-item-details {
    flex: 1;
}

/* تحسينات للمنتجات */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px 10px 0 0;
    background: var(--bg-secondary);
}

.product-card .product-header {
    padding: var(--spacing-sm);
}

.product-card .product-info {
    padding: 0 var(--spacing-sm) var(--spacing-sm);
}

/* ===== حالة المخزون ===== */
.stock-status-container {
    max-width: 700px;
    margin: 0 auto;
}

.product-info-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 10px;
}

.product-image-small {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.product-price {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--primary-color);
    font-weight: 600;
    font-size: var(--font-size-md);
}

.product-barcode {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-family: 'Courier New', monospace;
}

.stock-table-container {
    margin-bottom: var(--spacing-lg);
}

.stock-table-container h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.stock-status-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.stock-status-table th,
.stock-status-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.stock-status-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.stock-status-table tbody tr:hover {
    background: var(--bg-secondary);
}

.quantity-cell {
    font-weight: 600;
    font-size: var(--font-size-md);
    text-align: center;
}

.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.in-stock {
    background: var(--success-color);
    color: white;
}

.status-badge.low-stock {
    background: var(--warning-color);
    color: white;
}

.status-badge.out-of-stock {
    background: var(--error-color);
    color: white;
}

.total-row {
    background: var(--bg-secondary) !important;
    border-top: 2px solid var(--primary-color);
}

.total-row td {
    font-size: var(--font-size-md);
    border-bottom: none;
}

.stock-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 10px;
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.summary-item .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.summary-item .value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-md);
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

@media (max-width: 768px) {
    .product-info-header {
        flex-direction: column;
        text-align: center;
    }

    .stock-summary {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .stock-status-table {
        font-size: var(--font-size-sm);
    }

    .stock-status-table th,
    .stock-status-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== التصميم المتجاوب للجوال ===== */

/* تحسينات عامة للجوال */
@media (max-width: 768px) {
    /* تحسين الخط والمسافات */
    :root {
        --font-size-xs: 12px;
        --font-size-sm: 14px;
        --font-size-md: 16px;
        --font-size-lg: 18px;
        --font-size-xl: 20px;
        --spacing-xs: 4px;
        --spacing-sm: 8px;
        --spacing-md: 12px;
        --spacing-lg: 16px;
        --spacing-xl: 24px;
    }

    /* تحسين التطبيق الرئيسي */
    .main-app {
        grid-template-areas:
            "navbar"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    /* إخفاء الشريط الجانبي على الجوال */
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
        background: var(--bg-primary);
        box-shadow: var(--shadow-hover);
    }

    .sidebar.mobile-open {
        right: 0;
    }

    /* إضافة زر القائمة للجوال */
    .navbar {
        position: relative;
    }

    .mobile-menu-btn {
        display: block !important;
        background: none;
        border: none;
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        padding: var(--spacing-sm);
        cursor: pointer;
        border-radius: 8px;
        transition: var(--transition);
        margin-left: var(--spacing-sm);
    }

    .mobile-menu-btn:hover {
        background: var(--bg-secondary);
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    /* تحسين المحتوى الرئيسي */
    .main-content {
        padding: var(--spacing-md);
        margin-top: 0;
    }

    /* تحسين الأزرار للمس */
    .btn, .btn-icon, .qty-btn {
        min-height: 44px;
        min-width: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-sm {
        min-height: 36px;
        min-width: 36px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* تحسين النماذج */
    .form-group input,
    .form-group select,
    .form-group textarea {
        min-height: 44px;
        font-size: var(--font-size-md);
        padding: var(--spacing-sm) var(--spacing-md);
    }

    /* تحسين الشبكات */
    .products-grid,
    .customers-grid,
    .suppliers-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    /* تحسين البطاقات */
    .product-card,
    .customer-card,
    .supplier-card {
        padding: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-card h3 {
        font-size: var(--font-size-md);
    }

    .stat-card p {
        font-size: var(--font-size-xs);
    }

    /* تحسين النوافذ المنبثقة */
    .modal-content {
        width: 95%;
        max-width: none;
        margin: var(--spacing-md);
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    .modal-header h3 {
        font-size: var(--font-size-lg);
    }

    /* تحسين الجداول */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    table {
        min-width: 600px;
    }

    /* تحسين سلة التسوق */
    .cart-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .cart-item-info {
        flex-direction: row;
        align-items: center;
    }

    .cart-item-image {
        width: 50px;
        height: 50px;
    }

    .item-controls {
        justify-content: center;
        gap: var(--spacing-md);
    }

    .qty-btn {
        width: 44px;
        height: 44px;
    }

    .quantity {
        min-width: 44px;
        text-align: center;
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    /* تحسين أزرار الإجراءات */
    .form-actions,
    .product-actions,
    .customer-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .form-actions .btn {
        width: 100%;
    }

    /* تحسين البحث والفلاتر */
    .search-box input {
        font-size: var(--font-size-md);
        padding: var(--spacing-sm) var(--spacing-md);
        min-height: 44px;
    }

    .products-controls,
    .customers-controls,
    .suppliers-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    /* تحسين رفع الصور */
    .image-preview {
        width: 120px;
        height: 120px;
    }

    .image-upload-container {
        align-items: center;
    }

    /* تحسين التنقل */
    .nav-list {
        padding: var(--spacing-md);
    }

    .nav-list li a {
        padding: var(--spacing-md);
        font-size: var(--font-size-md);
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    /* تحسين الهيدر */
    .navbar-brand h1 {
        font-size: var(--font-size-lg);
    }

    .navbar-controls {
        gap: var(--spacing-sm);
    }

    .user-btn {
        padding: var(--spacing-sm);
        min-height: 44px;
    }

    /* تحسين التبويبات */
    .settings-tabs {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .cart-item-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .cart-item-image {
        align-self: center;
    }
}

/* تحسينات للاتجاه الأفقي على الجوال */
@media (max-width: 768px) and (orientation: landscape) {
    .main-content {
        padding: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .modal-content {
        max-height: 90vh;
    }
}

/* إضافة overlay للجوال عند فتح القائمة */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

@media (max-width: 768px) {
    .mobile-overlay.active {
        display: block;
    }
}

/* ===== تصميم موحد للفلاتر ===== */
.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 200px;
    flex: 1;
}

.filter-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.filter-select,
.filter-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1);
    min-height: 44px;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1),
                0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
    background: var(--bg-primary);
}

.filter-select:hover,
.filter-input:hover {
    border-color: var(--primary-color);
    background: var(--bg-primary);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 10px;
    background: var(--primary-color);
    color: white;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow);
}

.filter-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.filter-btn.secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.filter-btn.secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.filter-btn i {
    font-size: var(--font-size-sm);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .filters-container {
        flex-direction: column;
        align-items: stretch;
        padding: var(--spacing-md);
    }

    .filter-group {
        min-width: auto;
        width: 100%;
    }

    .filter-actions {
        justify-content: stretch;
        margin-top: var(--spacing-sm);
    }

    .filter-btn {
        flex: 1;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .filter-actions {
        flex-direction: column;
    }

    .filter-btn {
        width: 100%;
    }
}

/* تصميم خاص للبحث */
.search-filter-container {
    position: relative;
    flex: 2;
    min-width: 250px;
}

.search-filter-input {
    padding-right: 45px;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    width: 100%;
    min-height: 44px;
    font-size: var(--font-size-sm);
    transition: var(--transition);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1);
}

.search-filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1),
                0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
    background: var(--bg-primary);
}

.search-filter-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    pointer-events: none;
}

/* تحسينات للفلاتر المتقدمة */
.advanced-filters {
    display: none;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.advanced-filters.show {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.toggle-advanced-filters {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 5px;
    transition: var(--transition);
}

.toggle-advanced-filters:hover {
    background: var(--bg-secondary);
}

/* ===== منتقي التاريخ ===== */
.date-picker-container {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.date-picker-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-family: inherit;
    transition: var(--transition);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1);
    min-height: 44px;
    cursor: pointer;
    direction: ltr;
    text-align: right;
}

.date-picker-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1),
                0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
    background: var(--bg-primary);
}

.date-picker-input:hover {
    border-color: var(--primary-color);
    background: var(--bg-primary);
}

/* تخصيص أيقونة التقويم */
.date-picker-input::-webkit-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
    background-size: 16px 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    width: 20px;
    height: 20px;
    margin-left: 8px;
}

.date-picker-input::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* تخصيص للمتصفحات الأخرى */
.date-picker-input::-moz-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
    background-size: 16px 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    width: 20px;
    height: 20px;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .date-picker-input {
        font-size: var(--font-size-md);
        padding: var(--spacing-md);
    }
}

/* تصميم خاص لنطاق التاريخ */
.date-range-container {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.date-range-separator {
    color: var(--text-secondary);
    font-weight: 600;
    padding: 0 var(--spacing-xs);
}

.date-range-container .date-picker-input {
    flex: 1;
    min-width: 140px;
}

@media (max-width: 768px) {
    .date-range-container {
        flex-direction: column;
        align-items: stretch;
    }

    .date-range-separator {
        text-align: center;
        padding: var(--spacing-xs) 0;
    }

    .date-range-container .date-picker-input {
        min-width: auto;
    }
}

/* تحسينات للتاريخ في النماذج */
.form-group .date-picker-input {
    width: 100%;
}

/* تصميم خاص للفلاتر */
.filter-group .date-picker-input {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
}

.filter-group .date-picker-input:focus {
    background: var(--bg-primary);
    border-color: var(--primary-color);
}

/* ===== عرض رصيد العميل في المبيعات ===== */
.sales-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 10px;
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Customer and Warehouse selection now use standard filter styling */

/* Quick Actions Panel */
.quick-actions-section {
    margin: var(--spacing-lg) 0;
    background: var(--bg-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.section-title h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.section-title p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.action-group {
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.action-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.group-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.group-header i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateX(5px) scale(1.02);
}

.action-btn.warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.action-btn.warning:hover {
    background: #e67e22;
    transform: translateX(5px) scale(1.02);
}

.action-btn i {
    font-size: 1rem;
    min-width: 16px;
}

/* Enhanced Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable-card {
    cursor: pointer;
}

.stat-card.clickable-card:hover {
    background: var(--primary-light);
}

.card-action-hint {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    color: var(--text-secondary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .card-action-hint {
    opacity: 1;
}

/* Responsive Design for Quick Actions */
@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .action-group {
        padding: var(--spacing-sm);
    }

    .action-btn {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }

    .action-btn:hover {
        transform: none;
    }

    .section-title h3 {
        font-size: 1.3rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-sm);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-card:hover {
        transform: none;
    }
}

@media (max-width: 480px) {
    .quick-actions-section {
        padding: var(--spacing-md);
        margin: var(--spacing-md) 0;
    }

    .action-buttons {
        gap: var(--spacing-xs);
    }

    .action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.85rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Warehouse Distribution Styling */
.warehouse-distribution {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
}

.warehouse-qty-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    border-radius: 6px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.warehouse-qty-item.low-stock {
    background: #fff5f5;
    border-color: #fed7d7;
    box-shadow: 0 0 0 1px rgba(245, 101, 101, 0.2);
}

.warehouse-qty-item label {
    font-weight: 500;
    color: var(--text-primary);
    min-width: 120px;
}

.warehouse-qty-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    justify-content: flex-end;
}

.warehouse-qty-input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.warehouse-qty-input[readonly] {
    background: #f8f9fa;
    color: var(--text-secondary);
    cursor: not-allowed;
    border-style: dashed;
}

.low-stock-indicator {
    background: var(--error-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* Enhanced Form Styling */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input[readonly] {
    background: #f8f9fa;
    color: var(--text-secondary);
    cursor: not-allowed;
    border-style: dashed;
}

/* Mobile Responsive for Warehouse Distribution */
@media (max-width: 768px) {
    .warehouse-qty-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .warehouse-qty-item label {
        min-width: auto;
        text-align: center;
    }

    .warehouse-qty-display {
        justify-content: center;
    }

    .warehouse-distribution {
        max-height: 250px;
    }
}

/* Auto-generated Invoice Number Field Styling */
.auto-generated-field {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    font-weight: 600 !important;
    text-align: center !important;
    cursor: not-allowed !important;
    position: relative;
}

.auto-generated-field::before {
    content: '🔢';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    opacity: 0.7;
}

.auto-generated-field:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2) !important;
}

/* RTL support for auto-generated field */
[dir="rtl"] .auto-generated-field::before {
    left: auto;
    right: 10px;
}

/* Form group styling for invoice number */
.form-group:has(.auto-generated-field) label {
    color: var(--primary-color);
    font-weight: 600;
}

.form-group:has(.auto-generated-field) label::after {
    content: ' (تلقائي)';
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: normal;
}

/* Enhanced Warehouse Distribution Styling */
.warehouse-distribution-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    border: 2px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.warehouse-distribution-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.warehouse-distribution-section h4::before {
    content: '🏪';
    font-size: 1.2rem;
}

.distribution-info {
    background: var(--info-light);
    color: var(--info-color);
    padding: var(--spacing-md);
    border-radius: 8px;
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid var(--info-color);
}

.distribution-item {
    background: var(--bg-primary);
    border-radius: 10px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.distribution-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.distribution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.distribution-header h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
    font-size: var(--font-size-lg);
}

.total-quantity {
    background: var(--primary-light);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.warehouse-count {
    background: var(--success-light);
    color: var(--success-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin-left: var(--spacing-sm);
}

.warehouse-distribution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.warehouse-dist-item {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.warehouse-dist-item:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.warehouse-dist-item label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.warehouse-dist-input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: var(--font-size-sm);
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
}

.warehouse-dist-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.warehouse-dist-input:invalid {
    border-color: var(--error-color);
    background: var(--error-light);
}

.distribution-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.distributed-total {
    color: var(--success-color);
    font-weight: 600;
}

.remaining-total {
    color: var(--warning-color);
    font-weight: 600;
}

.distributed-total span,
.remaining-total span {
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-xs);
}

/* Real-time update animations */
.warehouse-distribution-section.updating {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.warehouse-dist-item.new-warehouse {
    animation: newWarehouseHighlight 2s ease-in-out;
}

@keyframes newWarehouseHighlight {
    0% {
        background: var(--success-light);
        border-color: var(--success-color);
        transform: scale(1.02);
    }
    50% {
        background: var(--success-light);
        border-color: var(--success-color);
    }
    100% {
        background: var(--bg-secondary);
        border-color: var(--border-color);
        transform: scale(1);
    }
}

/* Responsive design for warehouse distribution */
@media (max-width: 768px) {
    .warehouse-distribution-grid {
        grid-template-columns: 1fr;
    }

    .distribution-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .distribution-summary {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .warehouse-count {
        margin-left: 0;
        margin-top: var(--spacing-xs);
    }
}

/* Product Supplier Information Styling */
.product-supplier {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--info-light);
    border-radius: 8px;
    margin-top: var(--spacing-sm);
    border-left: 3px solid var(--info-color);
    font-size: var(--font-size-sm);
    color: var(--info-color);
    font-weight: 500;
}

.product-supplier i {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.product-supplier span {
    flex: 1;
}

/* RTL support for product supplier */
[dir="rtl"] .product-supplier {
    border-left: none;
    border-right: 3px solid var(--info-color);
}

/* Manual Tax and Discount Fields Styling */
.total-row.manual-input {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 2px solid var(--border-color);
    margin: var(--spacing-sm) 0;
    transition: all 0.2s ease;
}

.total-row.manual-input:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.total-row.manual-input label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 120px;
    margin: 0;
    font-size: var(--font-size-sm);
}

.total-row.manual-input input {
    flex: 1;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
    transition: all 0.2s ease;
    max-width: 120px;
}

.total-row.manual-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.total-row.manual-input input:invalid {
    border-color: var(--error-color);
    background: var(--error-light);
}

.total-row.manual-input span {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    min-width: 30px;
}

/* Enhanced purchase totals section */
.purchase-totals {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    border: 2px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.purchase-totals .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.purchase-totals .total-row:last-child {
    border-bottom: none;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 2px solid var(--primary-color);
    background: var(--primary-light);
    border-radius: 8px;
    padding: var(--spacing-md);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.purchase-totals .total-row.total span {
    color: var(--primary-color);
}

/* Supplier-Product Relationship Indicators */
.supplier-filter-info {
    background: var(--success-light);
    color: var(--success-color);
    padding: var(--spacing-md);
    border-radius: 8px;
    margin: var(--spacing-md) 0;
    border-left: 4px solid var(--success-color);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.supplier-filter-info i {
    font-size: var(--font-size-md);
}

.supplier-filter-info.no-supplier {
    background: var(--warning-light);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

/* RTL support for supplier filter info */
[dir="rtl"] .supplier-filter-info {
    border-left: none;
    border-right: 4px solid var(--success-color);
}

[dir="rtl"] .supplier-filter-info.no-supplier {
    border-right-color: var(--warning-color);
}

/* Product dropdown enhanced styling */
.item-product {
    position: relative;
}

.item-product:disabled {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
    opacity: 0.7;
}

.item-product option:disabled {
    color: var(--text-secondary);
    font-style: italic;
}

/* Responsive design for manual inputs */
@media (max-width: 768px) {
    .total-row.manual-input {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .total-row.manual-input label {
        min-width: auto;
        text-align: center;
    }

    .total-row.manual-input input {
        max-width: none;
    }

    .product-supplier {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs);
    }
}

.customer-balance-display {
    min-height: 24px;
    display: flex;
    align-items: center;
}

.customer-balance {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition);
}

.customer-balance.positive {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.customer-balance.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.customer-balance.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.customer-balance i {
    font-size: var(--font-size-xs);
}

/* Mobile responsive styles now handled by standard filter container */

/* ===== تحسين تصميم الفواتير ===== */
.invoice-form {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-neumorphic);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.invoice-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.invoice-number {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.invoice-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.invoice-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.invoice-form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.invoice-form-group input,
.invoice-form-group select,
.invoice-form-group textarea {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
    box-shadow: inset 2px 2px 4px rgba(0,0,0,0.1);
}

.invoice-form-group input:focus,
.invoice-form-group select:focus,
.invoice-form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: inset 2px 2px 4px rgba(0,0,0,0.1), 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.invoice-items-section {
    margin: var(--spacing-lg) 0;
}

.invoice-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.invoice-items-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-neumorphic);
    margin-bottom: var(--spacing-lg);
}

.invoice-table th {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    text-align: right;
    font-weight: 600;
    font-size: var(--font-size-sm);
    border-bottom: 2px solid var(--border-color);
}

.invoice-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    text-align: right;
    vertical-align: middle;
    font-size: var(--font-size-sm);
}

.invoice-table tbody tr:hover {
    background: rgba(74, 144, 226, 0.05);
}

.invoice-table tbody tr:last-child td {
    border-bottom: none;
}

.invoice-table input {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-secondary);
    text-align: center;
    font-size: var(--font-size-xs);
}

.invoice-summary {
    background: var(--bg-primary);
    border-radius: 10px;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-neumorphic);
}

.invoice-summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.invoice-summary-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.invoice-summary-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.invoice-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.invoice-summary-row:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: var(--font-size-md);
    color: var(--primary-color);
    border-top: 2px solid var(--primary-color);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.invoice-summary-label {
    font-weight: 600;
    color: var(--text-primary);
}

.invoice-summary-value {
    font-weight: 600;
    color: var(--text-secondary);
}

.invoice-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
    .invoice-form-grid {
        grid-template-columns: 1fr;
    }

    .invoice-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .invoice-summary-grid {
        grid-template-columns: 1fr;
    }

    .invoice-actions {
        flex-direction: column;
    }

    .invoice-table {
        font-size: var(--font-size-xs);
    }

    .invoice-table th,
    .invoice-table td {
        padding: var(--spacing-xs);
    }
}

/* ===== البطاقات القابلة للنقر ===== */
.clickable-card {
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.clickable-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.clickable-card .card-action-hint {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    opacity: 0;
    transition: var(--transition);
    color: var(--primary-color);
    font-size: var(--font-size-xs);
}

.clickable-card:hover .card-action-hint {
    opacity: 1;
}

/* ===== صفحة تفاصيل المخزون المنخفض ===== */
.low-stock-page {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 100vh;
}

.low-stock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.low-stock-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.low-stock-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.low-stock-summary-card {
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-neumorphic);
    text-align: center;
}

.low-stock-summary-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--error-color);
}

.low-stock-summary-card p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.warehouse-section {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-neumorphic);
}

.warehouse-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.warehouse-section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.warehouse-section-count {
    background: var(--error-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.low-stock-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-neumorphic);
}

.low-stock-table th {
    background: var(--error-color);
    color: white;
    padding: var(--spacing-md);
    text-align: right;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.low-stock-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    text-align: right;
    vertical-align: middle;
    font-size: var(--font-size-sm);
}

.low-stock-table tbody tr:hover {
    background: rgba(239, 68, 68, 0.05);
}

.low-stock-table tbody tr:last-child td {
    border-bottom: none;
}

.stock-status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-align: center;
}

.stock-status-badge.low-stock {
    background: rgba(251, 191, 36, 0.2);
    color: #d97706;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.stock-status-badge.out-of-stock {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

@media (max-width: 768px) {
    .low-stock-summary {
        grid-template-columns: 1fr;
    }

    .low-stock-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .warehouse-section-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .low-stock-table {
        font-size: var(--font-size-xs);
    }

    .low-stock-table th,
    .low-stock-table td {
        padding: var(--spacing-xs);
    }
}

/* ===== تفاصيل المخزن بحجم الصفحة الكاملة ===== */
.warehouse-details-fullpage {
    width: 100%;
    max-width: none;
    height: 100vh;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.warehouse-header-info {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.warehouse-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.warehouse-title h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.warehouse-status {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.warehouse-status.active {
    background: var(--success-color);
    color: white;
}

.warehouse-status.inactive {
    background: var(--error-color);
    color: white;
}

.warehouse-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 10px;
    transition: var(--transition);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.info-card i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    width: 24px;
    text-align: center;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-content .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.info-content .value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-md);
}

.warehouse-products-section {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.products-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.products-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.warehouse-products-table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.warehouse-products-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    min-width: 800px;
}

.warehouse-products-table th,
.warehouse-products-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.warehouse-products-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
    position: sticky;
    top: 0;
    z-index: 10;
}

.warehouse-products-table tbody tr:hover {
    background: var(--bg-secondary);
}

.product-mini-image {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    object-fit: cover;
    margin-left: var(--spacing-sm);
    vertical-align: middle;
}

.product-name {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.qty-input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    text-align: center;
    font-size: var(--font-size-sm);
}

.qty-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.no-products-message {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-style: italic;
    background: var(--bg-secondary);
    border-radius: 10px;
    margin-top: var(--spacing-md);
}

.warehouse-details-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding-top: var(--spacing-lg);
    border-top: 2px solid var(--border-color);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .warehouse-details-fullpage {
        padding: var(--spacing-md);
    }

    .warehouse-title {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .warehouse-info-grid {
        grid-template-columns: 1fr;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .products-actions {
        justify-content: stretch;
    }

    .products-actions .btn {
        flex: 1;
    }

    .warehouse-products-table {
        font-size: var(--font-size-sm);
    }

    .warehouse-products-table th,
    .warehouse-products-table td {
        padding: var(--spacing-sm);
    }
}

/* تحسين النوافذ المنبثقة بحجم الصفحة الكاملة */
.modal.fullpage .modal-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    margin: 0;
    border-radius: 0;
    overflow: hidden;
}

/* توزيع المخازن */
.warehouse-distribution {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.warehouse-qty-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.warehouse-qty-item label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.warehouse-qty-input {
    padding: var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.warehouse-qty-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* توزيع المخازن في المشتريات */
.warehouse-distribution-section {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.warehouse-distribution-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.distribution-info {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.distribution-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.warehouse-distribution-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.distribution-item {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: 10px;
    border: 2px solid var(--border-color);
    transition: var(--transition);
}

.distribution-item.valid-distribution {
    border-color: var(--success-color);
    background: rgba(var(--success-rgb), 0.05);
}

.distribution-item.invalid-distribution {
    border-color: var(--error-color);
    background: rgba(var(--error-rgb), 0.05);
}

.distribution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.distribution-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.total-quantity {
    color: var(--primary-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.warehouse-distribution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.warehouse-dist-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.warehouse-dist-item label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.warehouse-dist-input {
    padding: var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.warehouse-dist-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.distribution-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 6px;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.distributed-total {
    color: var(--primary-color);
}

.remaining-total {
    color: var(--text-secondary);
}

.modal.fullpage .modal-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--bg-primary);
    border-bottom: 2px solid var(--border-color);
}

.modal.fullpage .modal-body {
    height: calc(100vh - 80px);
    overflow-y: auto;
    padding: 0;
}

/* تحسين نماذج الشراء في وضع الصفحة الكاملة */
.modal.fullpage .modal-body form {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.modal.fullpage .purchase-items-section {
    margin: var(--spacing-lg) 0;
}

/* ===== إدارة المستخدمين ===== */
.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.user-card {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.user-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.username {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-family: 'Courier New', monospace;
}

.user-role {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.user-role.admin {
    background: var(--primary-color);
    color: white;
}

.user-role.manager {
    background: var(--warning-color);
    color: white;
}

.user-role.cashier {
    background: var(--info-color);
    color: white;
}

.user-role.viewer {
    background: var(--text-secondary);
    color: white;
}

.user-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.user-status.active {
    background: var(--success-color);
    color: white;
}

.user-status.inactive {
    background: var(--error-color);
    color: white;
}

.user-details {
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.detail-item .value {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.user-actions .btn {
    padding: var(--spacing-xs);
    min-width: auto;
}

@media (max-width: 768px) {
    .users-grid {
        grid-template-columns: 1fr;
    }

    .user-header {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .user-actions {
        justify-content: center;
    }
}

/* ===== التطبيق الرئيسي ===== */
.main-app {
    display: grid;
    grid-template-areas: 
        "navbar navbar"
        "sidebar content";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 70px 1fr;
    height: 100vh;
}

/* ===== شريط التنقل العلوي ===== */
.top-navbar {
    grid-area: navbar;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-xl);
    z-index: 100;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
}

.navbar-brand i {
    font-size: 1.5rem;
}

.navbar-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.theme-toggle {
    background: var(--bg-primary);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    color: var(--text-primary);
}

.theme-toggle:hover {
    box-shadow: var(--shadow-hover);
}

.user-menu {
    position: relative;
}

.user-btn {
    background: var(--bg-primary);
    border: none;
    border-radius: 25px;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    color: var(--text-primary);
}

.user-btn:hover {
    box-shadow: var(--shadow-hover);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-secondary);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    padding: var(--spacing-sm);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-menu:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background: var(--bg-primary);
}

/* ===== الشريط الجانبي ===== */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: var(--spacing-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 15px;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
    font-weight: 500;
}

.nav-link:hover {
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-light);
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    grid-area: content;
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: var(--bg-primary);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--bg-tertiary);
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
}

.date-info {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 10px;
    box-shadow: var(--shadow-light);
    color: var(--text-secondary);
}

/* ===== بطاقات الإحصائيات ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ===== لوحة المعلومات ===== */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
}

.chart-container,
.alerts-container {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
}

.chart-container h3,
.alerts-container h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.alerts-list {
    max-height: 300px;
    overflow-y: auto;
}

/* ===== النوافذ المنبثقة ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--bg-tertiary);
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--error-color);
}

.modal-body {
    padding: var(--spacing-xl);
    overflow-y: auto;
    max-height: 60vh;
}

/* ===== أنماط المنتجات ===== */
.products-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: 15px;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.product-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 0;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

/* حاوي صورة المنتج */
.product-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
    background: var(--bg-primary);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    color: var(--text-secondary);
    font-size: 3rem;
}

.product-status-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 15px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: white;
    background: var(--success-color);
}

.product-status-badge.low-stock {
    background: var(--warning-color);
}

.product-status-badge.out-of-stock {
    background: var(--danger-color);
}

/* تحسينات حالة المخزون للبطاقة */
.product-card.low-stock {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, var(--bg-secondary), rgba(255, 193, 7, 0.05));
}

.product-card.out-of-stock {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, var(--bg-secondary), rgba(220, 53, 69, 0.05));
    opacity: 0.9;
}

.product-card.out-of-stock:hover {
    transform: translateY(-2px);
    opacity: 1;
}

/* تأثيرات إضافية للصورة */
.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-card.out-of-stock .product-image {
    filter: grayscale(50%);
}

.product-card.out-of-stock:hover .product-image {
    filter: grayscale(0%);
}

/* عنوان المنتج */
.product-header {
    padding: var(--spacing-md) var(--spacing-lg) 0;
    text-align: center;
}

.product-header .product-name {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
    min-height: 2.6em;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* أزرار العمليات */
.product-actions {
    display: flex;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-sm);
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    justify-content: center;
}

.product-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border-radius: 8px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    transition: var(--transition);
    border: 1px solid transparent;
    min-height: 36px;
}

.product-actions .btn i {
    font-size: var(--font-size-sm);
}

.product-actions .btn span {
    display: none;
}

.product-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.product-actions .btn-info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border-color: rgba(23, 162, 184, 0.2);
}

.product-actions .btn-info:hover {
    background: #17a2b8;
    color: white;
}

.product-actions .btn-primary {
    background: rgba(74, 144, 226, 0.1);
    color: var(--primary-color);
    border-color: rgba(74, 144, 226, 0.2);
}

.product-actions .btn-primary:hover {
    background: var(--primary-color);
    color: white;
}

.product-actions .btn-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-color: rgba(220, 53, 69, 0.2);
}

.product-actions .btn-danger:hover {
    background: var(--danger-color);
    color: white;
}

/* معلومات المنتج */
.product-info {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    flex: 1;
}

.product-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.product-detail:last-child {
    border-bottom: none;
}

.product-detail .label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.product-detail .value {
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.product-detail .value.price {
    color: var(--success-color);
    font-size: var(--font-size-md);
}

.product-detail .value.stock {
    font-weight: 700;
}

.product-detail .value.stock.low-stock {
    color: var(--warning-color);
}

.product-detail .value.stock.out-of-stock {
    color: var(--danger-color);
}

.product-description {
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.product-description .label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.product-description .description-text {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: 8px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* ===== أنماط النماذج ===== */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: 10px;
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
}

/* ===== أنماط الأزرار ===== */
.btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 10px;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    box-shadow: var(--shadow-light);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.btn-purple:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .main-app {
        grid-template-areas:
            "navbar"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 70px;
        right: -280px;
        height: calc(100vh - 70px);
        width: 280px;
        z-index: 200;
        transition: var(--transition);
    }

    .sidebar.open {
        right: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .products-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* ===== أنماط المبيعات ===== */
.sales-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.products-panel,
.cart-panel {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.products-search {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.products-search input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: 15px;
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.products-search i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.products-list {
    flex: 1;
    overflow-y: auto;
    padding-right: var(--spacing-sm);
}

.sales-product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: 15px;
    cursor: pointer;
    transition: var(--transition);
}

.sales-product-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-hover);
}

.sales-product-item .product-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.sales-product-item .product-price {
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.sales-product-item .product-stock {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.sales-product-item .add-btn {
    width: 35px;
    height: 35px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.sales-product-item:hover .add-btn {
    transform: scale(1.1);
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--bg-primary);
}

.cart-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: var(--spacing-lg);
    padding-right: var(--spacing-sm);
}

.empty-cart {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.cart-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: var(--spacing-md);
    align-items: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: 15px;
    transition: var(--transition);
}

.cart-item:hover {
    box-shadow: var(--shadow-hover);
}

.cart-item .item-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.cart-item .item-price {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.qty-btn {
    width: 25px;
    height: 25px;
    border: none;
    border-radius: 50%;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.qty-btn:hover {
    background: var(--primary-color);
    color: white;
}

.quantity {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 20px;
    text-align: center;
}

.item-total {
    font-weight: 600;
    color: var(--primary-color);
}

.remove-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: var(--error-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.remove-btn:hover {
    transform: scale(1.1);
}

.cart-summary {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: 15px;
    margin-bottom: var(--spacing-lg);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.summary-row.total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    border-top: 2px solid var(--bg-secondary);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    color: var(--primary-color);
}

.payment-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.payment-method label,
.payment-amount label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.payment-method select,
.payment-amount input {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: 10px;
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    color: var(--text-primary);
}

.change-amount {
    margin-top: var(--spacing-sm);
    font-weight: 600;
}

.change-positive {
    color: var(--success-color);
    font-weight: 600;
}

.change-negative {
    color: var(--error-color);
    font-weight: 600;
}

.change-complete {
    color: var(--success-color);
    font-weight: 600;
    background: rgba(34, 197, 94, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 6px;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

/* التصميم المتجاوب للمبيعات */
@media (max-width: 1024px) {
    .sales-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
        height: auto;
    }

    .cart-panel {
        max-height: 500px;
    }
}

/* ===== تصميم المبيعات الجديد ===== */
.new-sales-layout {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.product-selection-panel {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    align-items: end;
}

.product-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.product-selector label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.product-selector select {
    padding: var(--spacing-md);
    border: none;
    border-radius: 15px;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
    cursor: pointer;
}

.product-selector select:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.barcode-search {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.barcode-search input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: 15px;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.barcode-search input:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.barcode-search i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    margin-top: 1.2rem;
}

.central-cart-panel {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 500px;
}

.central-cart-panel .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--bg-secondary);
}

.central-cart-panel .cart-header h3 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin: 0;
}

.central-cart-panel .cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: var(--spacing-lg);
    min-height: 200px;
}

.central-cart-panel .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
    text-align: center;
}

.central-cart-panel .empty-cart i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.central-cart-panel .empty-cart p {
    margin: var(--spacing-sm) 0;
}

.central-cart-panel .empty-cart-hint {
    font-size: var(--font-size-sm);
    opacity: 0.7;
}

.central-cart-panel .cart-summary {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.central-cart-panel .payment-section {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
}

/* ضوابط الضرائب والخصومات */
.tax-discount-controls {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.control-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.control-group input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 10px;
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.control-group input:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.control-group input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* التصميم المتجاوب للمبيعات الجديد */
@media (max-width: 768px) {
    .product-selection-panel {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .new-sales-layout {
        height: auto;
    }

    .central-cart-panel {
        min-height: 400px;
    }
}

/* تبويبات المبيعات */
.sales-tabs {
    display: flex;
    gap: var(--spacing-sm);
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 15px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tab-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.sales-tab {
    display: none;
}

.sales-tab.active {
    display: block;
}

/* الفواتير السابقة */
.invoices-controls {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-lg);
    align-items: center;
}

.filter-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.filter-controls select,
.filter-controls input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 10px;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.invoices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.invoice-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.invoice-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--bg-secondary);
}

.invoice-info h3 {
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
}

.invoice-date,
.invoice-time {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

.invoice-total {
    text-align: left;
}

.total-amount {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.payment-method {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 8px;
}

.invoice-details {
    margin-bottom: var(--spacing-lg);
}

.invoice-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* تفاصيل الفاتورة */
.invoice-details-view {
    max-width: 800px;
    margin: 0 auto;
}

.invoice-summary {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item .label {
    font-weight: 600;
    color: var(--text-secondary);
}

.summary-item .value {
    color: var(--text-primary);
    font-weight: 500;
}

.invoice-items {
    margin-bottom: var(--spacing-lg);
}

.items-table {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    align-items: center;
}

.table-header {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.table-row {
    border-bottom: 1px solid var(--bg-primary);
}

.table-row:last-child {
    border-bottom: none;
}

.invoice-totals {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
}

.totals-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.total-row.final-total {
    border-top: 2px solid var(--primary-color);
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-md);
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

/* مجموعة إدخال الباركود */
.barcode-input-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.barcode-input-group input {
    flex: 1;
}

.barcode-input-group .btn {
    white-space: nowrap;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* أزرار التقارير */
.report-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.report-actions .btn {
    white-space: nowrap;
}

/* تحسينات طباعة التقارير */
.print-report {
    font-family: 'Cairo', sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    line-height: 1.6;
}

.print-report .report-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 1rem;
}

.print-report .report-header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.print-report .report-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.print-report .report-summary {
    margin-bottom: 2rem;
}

.print-report .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.print-report .summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.print-report .summary-item .label {
    font-weight: 600;
}

.print-report .customers-table,
.print-report .suppliers-table,
.print-report .payments-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.print-report .customers-table th,
.print-report .customers-table td,
.print-report .suppliers-table th,
.print-report .suppliers-table td,
.print-report .payments-table th,
.print-report .payments-table td {
    padding: 0.5rem;
    border: 1px solid #ddd;
    text-align: right;
}

.print-report .customers-table th,
.print-report .suppliers-table th,
.print-report .payments-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.print-report .report-footer {
    margin-top: 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
}

/* تحسينات لوحة المعلومات */
.date-time-info {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.date-info, .time-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.hijri-date, .gregorian-date, .current-time, .day-of-week {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.hijri-date {
    color: var(--primary-color);
    font-weight: 600;
}

.current-time {
    color: var(--success-color);
    font-weight: 600;
    font-size: var(--font-size-md);
}

.day-of-week {
    color: var(--text-primary);
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* تحسينات سجل المعاملات */
.customer-history {
    max-width: 800px;
    margin: 0 auto;
}

.history-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.history-header h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.transactions-list {
    max-height: 500px;
    overflow-y: auto;
    margin-bottom: var(--spacing-lg);
}

.transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: 10px;
    border-right: 4px solid;
    transition: var(--transition);
}

.transaction-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-hover);
}

.transaction-item.sale {
    border-color: var(--error-color);
}

.transaction-item.payment {
    border-color: var(--success-color);
}

.transaction-info {
    flex: 1;
}

.transaction-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.transaction-date {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.transaction-notes {
    margin: var(--spacing-xs) 0 0 0;
    color: var(--text-light);
    font-size: var(--font-size-xs);
    font-style: italic;
}

.transaction-amount {
    font-weight: 600;
    font-size: var(--font-size-md);
    margin: 0 var(--spacing-md);
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--error-color);
}

.transaction-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.transaction-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    min-width: auto;
    font-size: var(--font-size-xs);
}

.history-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* تحسينات تفاصيل الفاتورة */
.invoice-details {
    max-width: 800px;
    margin: 0 auto;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.company-info h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.company-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.invoice-info {
    text-align: left;
}

.invoice-info h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.invoice-info p {
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-lg);
}

.invoice-table th,
.invoice-table td {
    padding: var(--spacing-sm);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.invoice-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.invoice-summary {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 10px;
    margin-bottom: var(--spacing-lg);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.summary-row.total {
    border-top: 2px solid var(--primary-color);
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    font-weight: 600;
    font-size: var(--font-size-md);
    color: var(--primary-color);
}

.invoice-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
    .date-time-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .invoice-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .invoice-info {
        text-align: right;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .transaction-actions {
        align-self: flex-end;
    }
}

@media print {
    .print-report {
        padding: 0;
    }

    .print-report .report-header {
        border-bottom: 2px solid #000;
    }

    .print-report .customers-table th,
    .print-report .suppliers-table th,
    .print-report .payments-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}

/* ===== أنماط العملاء والموردين ===== */
.customers-controls,
.suppliers-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.customers-grid,
.suppliers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.customer-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.customer-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.customer-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.customer-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.customer-phone {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
    direction: ltr;
    text-align: right;
}

/* ===== إصلاح اتجاه النص لأرقام الهواتف ===== */

/* أرقام الهواتف في البطاقات */
.customer-phone,
.supplier-phone {
    direction: ltr !important;
    text-align: right !important;
    unicode-bidi: plaintext;
}

/* أرقام الهواتف في النماذج */
input[type="tel"],
input[id*="Phone"],
input[id*="phone"] {
    direction: ltr !important;
    text-align: left !important;
    unicode-bidi: plaintext;
}

/* أرقام الهواتف في الجداول */
td:has-text("phone"),
td:contains("+"),
.phone-number,
.contact-info {
    direction: ltr !important;
    text-align: right !important;
    unicode-bidi: plaintext;
}

/* أرقام الهواتف في التقارير والفواتير */
.invoice-info p:contains("هاتف"),
.company-info p:contains("هاتف"),
.customer-info p:contains("الهاتف"),
.supplier-info p:contains("الهاتف") {
    direction: ltr !important;
    unicode-bidi: plaintext;
}

/* أرقام الهواتف في ملخص البيانات */
.summary-item .value:contains("+"),
.summary-grid .value:contains("+") {
    direction: ltr !important;
    text-align: right !important;
    unicode-bidi: plaintext;
}

/* ===== أنماط صفحة الإعدادات ===== */
.settings-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
}

.settings-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: var(--transition);
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.settings-card-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.settings-card-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.settings-card-body {
    padding: var(--spacing-xl);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.settings-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.backup-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.backup-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.backup-section h4 {
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-md);
}

.backup-section p {
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-sm);
}

.file-input-container {
    position: relative;
    display: inline-block;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.file-input + label {
    cursor: pointer;
    margin: 0;
}

.system-info {
    display: grid;
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: var(--text-primary);
}

.info-value {
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.text-danger {
    color: #dc3545 !important;
}

/* ===== أنماط نقطة البيع (POS) ===== */
.pos-container {
    padding: var(--spacing-lg);
}

/* قسم معلومات الفاتورة */
.invoice-info-section {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: 15px;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-color);
}

.invoice-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    align-items: end;
}

.invoice-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.invoice-field label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.invoice-field select,
.invoice-field input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    transition: var(--transition);
}

.invoice-field select:focus,
.invoice-field input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.invoice-number-display {
    background: var(--bg-primary);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    font-family: 'Courier New', monospace;
    font-weight: 700;
    font-size: var(--font-size-md);
}

.invoice-prefix {
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    margin-right: var(--spacing-xs);
}

.invoice-number {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 900;
}

.pos-layout {
    display: grid;
    grid-template-columns: 1.5fr 1.2fr;
    gap: var(--spacing-xl);
    min-height: 700px;
    align-items: start;
}

/* قسم المنتجات */
.products-section {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.products-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.products-filters {
    display: flex;
    gap: var(--spacing-md);
}

.products-filters input,
.products-filters select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-sm);
    max-height: 450px;
    overflow-y: auto;
    padding-right: var(--spacing-xs);
}

.product-card {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.product-card.out-of-stock {
    opacity: 0.6;
    cursor: not-allowed;
}

.product-card.out-of-stock:hover {
    transform: none;
    border-color: transparent;
}

.product-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: var(--spacing-sm);
}

.product-info {
    text-align: center;
}

.product-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.3;
}

.product-price {
    font-size: var(--font-size-md);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 var(--spacing-xs) 0;
}

.product-stock {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0;
}

.warehouse-stock {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
    padding: var(--spacing-xs);
    background: var(--bg-secondary);
    border-radius: 5px;
}

.warehouse-stock.low-stock {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.warehouse-stock.out-of-stock {
    color: #95a5a6;
    background: rgba(149, 165, 166, 0.1);
}

/* قسم السلة */
.cart-section {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-medium);
    display: flex;
    flex-direction: column;
    border: 2px solid var(--primary-color);
    min-height: 650px;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 3px solid var(--primary-color);
}

.cart-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cart-header h3::before {
    content: "🛒";
    font-size: 1.2em;
}

.cart-items {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: var(--spacing-lg);
    padding-right: var(--spacing-xs);
}

.cart-item {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.cart-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.cart-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.cart-item-name {
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    margin: 0;
    line-height: 1.3;
}

.cart-item-remove {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 5px;
    transition: var(--transition);
}

.cart-item-remove:hover {
    background: rgba(231, 76, 60, 0.1);
}

.cart-item-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.quantity-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 700;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.quantity-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.quantity-display {
    min-width: 40px;
    text-align: center;
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 6px;
}

.cart-item-total {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 6px;
}

.cart-summary {
    border-top: 3px solid var(--primary-color);
    padding-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-md);
    font-weight: 600;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.summary-row.total-row {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    border-top: 2px solid var(--primary-color);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-md);
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 8px;
}

.discount-input,
.tax-input {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.discount-input input,
.tax-input input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--bg-primary);
    color: var(--text-primary);
    text-align: center;
}

.discount-input select,
.tax-input select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.cart-actions {
    border-top: 2px solid var(--border-color);
    padding-top: var(--spacing-lg);
}

.customer-selection {
    margin-bottom: var(--spacing-lg);
}

.customer-selection label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.customer-selection select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
}

.payment-methods {
    margin-bottom: var(--spacing-lg);
}

.payment-methods label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.payment-options {
    display: flex;
    gap: var(--spacing-md);
}

.radio-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 8px;
    transition: var(--transition);
}

.radio-label:hover {
    background: rgba(74, 144, 226, 0.1);
}

.radio-label input[type="radio"] {
    margin: 0;
}

.btn-large {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: var(--font-size-md);
    font-weight: 700;
}

.empty-cart {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-xl);
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* تجاوبية نقطة البيع */
@media (max-width: 1024px) {
    .pos-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        max-height: 400px;
    }

    .cart-items {
        max-height: 250px;
    }
}

@media (max-width: 768px) {
    .warehouse-selection {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .warehouse-selection select {
        max-width: none;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .products-filters {
        flex-direction: column;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .payment-options {
        flex-direction: column;
    }
}

/* ===== أنماط توزيع المخازن ===== */
.warehouse-distribution-section {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-xl);
    margin: var(--spacing-lg) 0;
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-color);
}

.distribution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.distribution-title {
    margin: 0;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.distribution-title::before {
    content: "📦";
    font-size: 1.2em;
}

.distribution-item {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.distribution-item:last-child {
    margin-bottom: 0;
}

.distribution-item .distribution-header {
    background: none;
    border: none;
    padding: 0 0 var(--spacing-md) 0;
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.distribution-item .product-name {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
    font-weight: 600;
}

.distribution-item .total-quantity {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.distribution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.warehouse-distribution {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.warehouse-distribution:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
}

.warehouse-distribution label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.warehouse-qty-input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    text-align: center;
    font-weight: 600;
    transition: var(--transition);
}

.warehouse-qty-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.warehouse-distribution .current-stock {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    text-align: center;
}

.distribution-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.distributed-total,
.remaining-total {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.distributed-total {
    color: var(--primary-color);
}

.remaining-total {
    color: var(--text-secondary);
}

.remaining-total span {
    font-weight: 700;
    font-size: var(--font-size-md);
}

/* تجاوبية توزيع المخازن */
@media (max-width: 768px) {
    .distribution-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .distribution-grid {
        grid-template-columns: 1fr;
    }

    .distribution-summary {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

/* ===== أنماط تعديل الفواتير ===== */
.edit-invoice-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input[readonly] {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.invoice-number-input {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    overflow: hidden;
    transition: var(--transition);
}

.invoice-number-input:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.invoice-prefix {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    font-family: 'Courier New', monospace;
    font-weight: 600;
    border-right: 1px solid var(--border-color);
    white-space: nowrap;
}

.invoice-number-input input {
    border: none;
    background: transparent;
    padding: var(--spacing-sm);
    flex: 1;
    font-family: 'Courier New', monospace;
    font-weight: 700;
    text-align: center;
    color: var(--primary-color);
}

.invoice-number-input input:focus {
    outline: none;
    box-shadow: none;
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.items-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
}

.items-table th,
.items-table td {
    padding: var(--spacing-sm);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.items-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.items-table td {
    color: var(--text-primary);
}

.items-table tr:last-child td {
    border-bottom: none;
}

.totals-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 300px;
    margin-left: auto;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.total-row:last-child {
    border-bottom: none;
}

.total-row.final-total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    border-top: 2px solid var(--primary-color);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-lg);
}

/* تجاوبية تعديل الفواتير */
@media (max-width: 768px) {
    .edit-invoice-form {
        max-width: none;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .totals-grid {
        max-width: none;
        margin-left: 0;
    }

    .form-actions {
        flex-direction: column;
    }

    .invoice-number-input {
        flex-direction: column;
    }

    .invoice-prefix {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
    }
}

/* ===== أنماط الفئات المحسنة ===== */
.category-select-enhanced {
    position: relative;
}

.category-select-enhanced select {
    padding-right: 40px;
}

.category-select-enhanced::after {
    content: "📂";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    font-size: 16px;
}

.category-option-with-description {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.category-option-with-description:last-child {
    border-bottom: none;
}

.category-name {
    font-weight: 600;
    color: var(--text-primary);
}

.category-description {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* تحسينات إضافية للفئات */
.category-filter-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 2px solid var(--primary-color);
}

.category-filter-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.category-filter-section h4::before {
    content: "🏷️";
    font-size: 1.2em;
}

/* ===== أنماط التقارير ===== */
.reports-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: var(--spacing-lg);
    overflow-y: auto;
    background: var(--bg-primary);
}

/* وضع الصفحة الكاملة للتقارير */
.reports-container.fullpage {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: var(--bg-primary);
    padding: var(--spacing-md);
}

.reports-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    border-bottom: 3px solid var(--primary-color);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 15px 15px 0 0;
    box-shadow: var(--shadow-light);
}

.tab-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    font-family: var(--font-family);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 150px;
    justify-content: center;
}

.tab-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn i {
    font-size: var(--font-size-md);
}

.reports-content {
    min-height: calc(100vh - 200px);
    background: var(--bg-primary);
    border-radius: 0 0 15px 15px;
    box-shadow: var(--shadow-medium);
}

.reports-container.fullpage .reports-content {
    min-height: calc(100vh - 150px);
}

.report-tab-content {
    display: none;
}

.report-tab-content.active {
    display: block;
}

.report-section {
    background: var(--bg-primary);
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.report-header {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.report-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.report-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filters-panel {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 600;
    color: var(--text-primary);
    margin-top: var(--spacing-lg);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.report-summary {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.summary-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.summary-card.alert {
    border-color: var(--danger-color);
    background: rgba(220, 53, 69, 0.1);
}

.summary-card.warning {
    border-color: var(--warning-color);
    background: rgba(255, 193, 7, 0.1);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.summary-card.alert .summary-icon {
    background: var(--danger-color);
}

.summary-card.warning .summary-icon {
    background: var(--warning-color);
}

.summary-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.summary-value {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.report-table-container {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
}

.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

.loading-indicator i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

.empty-report {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.empty-report i {
    font-size: 4rem;
    color: var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.empty-report h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.empty-report p {
    margin: 0;
    font-size: var(--font-size-md);
}

.report-table-wrapper {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.report-table th,
.report-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.report-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.report-table th:hover {
    background: var(--primary-color);
    color: white;
}

.report-table th i {
    margin-left: var(--spacing-xs);
    opacity: 0.7;
}

.report-table td {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.report-table tr:hover {
    background: rgba(74, 144, 226, 0.05);
}

.report-table tr:last-child td {
    border-bottom: none;
}

.report-table .total-row {
    background: var(--bg-secondary);
    font-weight: 600;
    border-top: 2px solid var(--primary-color);
}

.report-table .total-row td {
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

.products-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    white-space: nowrap;
}

.status-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.report-table tr.status-danger {
    background: rgba(220, 53, 69, 0.05);
}

.report-table tr.status-warning {
    background: rgba(255, 193, 7, 0.05);
}

/* طباعة التقارير */
@media print {
    .reports-tabs,
    .report-header,
    .filters-panel,
    .report-actions {
        display: none !important;
    }

    .report-section {
        box-shadow: none;
        border: none;
    }

    .report-table {
        font-size: 12px;
    }

    .report-table th,
    .report-table td {
        padding: 8px;
    }

    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .summary-card {
        padding: 10px;
    }

    .summary-icon {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .summary-value {
        font-size: 16px;
    }
}

/* تجاوبية التقارير */
@media (max-width: 768px) {
    .reports-container {
        padding: var(--spacing-md);
    }

    .reports-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .tab-btn {
        border-radius: 8px;
        min-width: auto;
    }

    .report-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .report-actions {
        justify-content: center;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .report-table {
        min-width: 600px;
    }

    .report-table th,
    .report-table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
}

/* ===== تحسينات إضافية لنقطة البيع ===== */
.pos-container {
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

/* تحسين عرض المنتجات */
.products-section .section-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg)) var(--spacing-lg) calc(-1 * var(--spacing-lg));
    border-radius: 15px 15px 0 0;
}

.products-section .section-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: white;
}

.products-section .section-header h3::before {
    content: "🛍️";
    font-size: 1.2em;
}

/* تحسين السلة */
.cart-section .cart-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    margin: calc(-1 * var(--spacing-xl)) calc(-1 * var(--spacing-xl)) var(--spacing-lg) calc(-1 * var(--spacing-xl));
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.cart-section .cart-header h3 {
    color: white;
}

/* تجاوبية محسنة لنقطة البيع */
@media (max-width: 1200px) {
    .pos-layout {
        grid-template-columns: 1.3fr 1.4fr;
    }
}

@media (max-width: 768px) {
    .pos-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        max-height: 300px;
    }

    .cart-items {
        max-height: 250px;
    }

    .cart-section {
        min-height: 500px;
    }
}

/* ===== تحسينات بطاقات المنتجات ===== */

/* إظهار نص الأزرار على الشاشات الكبيرة */
@media (min-width: 1200px) {
    .product-actions .btn span {
        display: inline;
    }

    .product-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 1024px) {
    .product-image-container {
        height: 180px;
    }

    .product-actions .btn {
        padding: var(--spacing-xs);
        min-height: 32px;
    }

    .product-actions .btn i {
        font-size: var(--font-size-xs);
    }
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .product-card {
        border-radius: 15px;
    }

    .product-image-container {
        height: 150px;
        border-radius: 15px 15px 0 0;
    }

    .product-actions {
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .product-actions .btn {
        padding: var(--spacing-xs);
        min-height: 28px;
        border-radius: 6px;
    }

    .product-actions .btn i {
        font-size: var(--font-size-xs);
    }

    .product-header {
        padding: var(--spacing-sm) var(--spacing-md) 0;
    }

    .product-header .product-name {
        font-size: var(--font-size-md);
    }

    .product-info {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .product-detail {
        padding: var(--spacing-xs) 0;
    }

    .product-detail .label,
    .product-detail .value {
        font-size: var(--font-size-xs);
    }

    .product-detail .value.price {
        font-size: var(--font-size-sm);
    }
}

/* ===== أنماط تقرير المخزون المحسن ===== */
.warehouse-out-of-stock {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: var(--danger-color) !important;
    font-weight: 700;
}

.warehouse-low-stock {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: var(--warning-color) !important;
    font-weight: 600;
}

.affected-warehouses {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.warehouse-tag {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    white-space: nowrap;
}

.warehouse-tag.danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.warehouse-tag.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.no-issues {
    color: var(--success-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.warehouse-details {
    margin-top: var(--spacing-sm);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.warehouse-mini-tag {
    display: inline-block;
    padding: 2px 6px;
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-radius: 8px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* تحسين عرض الجدول للمخازن المتعددة */
.report-table th:nth-last-child(1),
.report-table th:nth-last-child(2) {
    min-width: 150px;
}

.report-table td.warehouse-out-of-stock::before {
    content: "⚠️ ";
    margin-left: var(--spacing-xs);
}

.report-table td.warehouse-low-stock::before {
    content: "⚡ ";
    margin-left: var(--spacing-xs);
}

/* تحسين التجاوبية لتقرير المخزون */
@media (max-width: 768px) {
    .affected-warehouses {
        flex-direction: column;
    }

    .warehouse-tag {
        text-align: center;
    }

    .report-table th:nth-last-child(1),
    .report-table th:nth-last-child(2) {
        min-width: 120px;
        font-size: var(--font-size-xs);
    }
}

.customer-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.customer-details {
    margin-bottom: var(--spacing-lg);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.detail-item i {
    width: 16px;
    color: var(--text-light);
}

.balance-item {
    font-weight: 600;
    padding: var(--spacing-sm);
    border-radius: 8px;
    background: var(--bg-primary);
}

.balance-item.positive {
    color: var(--success-color);
}

.balance-item.negative {
    color: var(--error-color);
}

.balance-item.zero {
    color: var(--text-secondary);
}

.customer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--bg-primary);
}

.join-date {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.customer-quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* تفاصيل العميل */
.customer-details-view {
    max-width: 600px;
}

.customer-summary h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.summary-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.summary-item .label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-item .value {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    font-weight: 600;
}

.summary-item .value.positive {
    color: var(--success-color);
}

.summary-item .value.negative {
    color: var(--error-color);
}

.customer-actions-section {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* نموذج الدفعة */
.payment-info {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: 15px;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.payment-info h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.payment-info p {
    margin: 0;
    color: var(--text-secondary);
}

.payment-info .positive {
    color: var(--success-color);
    font-weight: 600;
}

.payment-info .negative {
    color: var(--error-color);
    font-weight: 600;
}

/* التصميم المتجاوب للعملاء والموردين */
@media (max-width: 768px) {
    .customers-controls,
    .suppliers-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .customers-grid,
    .suppliers-grid {
        grid-template-columns: 1fr;
    }

    .customer-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .customer-actions {
        align-self: flex-end;
    }

    .customer-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .customer-actions-section {
        flex-direction: column;
    }
}

/* ===== أنماط الإعدادات ===== */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.settings-tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 15px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    text-align: right;
    width: 100%;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    transform: translateX(-5px);
}

.tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-light);
}

.tab-btn i {
    font-size: 1.2rem;
}

.settings-content {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-xl);
    min-height: 500px;
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.settings-tab h3 {
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    border-bottom: 2px solid var(--bg-secondary);
    padding-bottom: var(--spacing-md);
}

/* أنماط النسخ الاحتياطي */
.backup-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.backup-item {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.backup-item h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.backup-item p {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* أنماط سجل المعاملات */
.customer-history {
    max-width: 700px;
}

.history-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--bg-primary);
}

.history-header h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.history-header p {
    margin: 0;
    color: var(--text-secondary);
}

.transactions-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: var(--spacing-xl);
    padding-right: var(--spacing-sm);
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: 15px;
    border-right: 4px solid;
    transition: var(--transition);
}

.transaction-item.sale {
    border-color: var(--error-color);
}

.transaction-item.payment {
    border-color: var(--success-color);
}

.transaction-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-hover);
}

.transaction-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.transaction-date {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.transaction-notes {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: var(--spacing-xs) 0 0 0;
    font-style: italic;
}

.transaction-amount {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--error-color);
}

.history-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* التصميم المتجاوب للإعدادات */
@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .settings-tabs {
        flex-direction: row;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }

    .tab-btn {
        min-width: 150px;
        flex-shrink: 0;
    }

    .backup-section {
        gap: var(--spacing-lg);
    }

    .history-actions {
        flex-direction: column;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .transaction-amount {
        align-self: flex-end;
    }
}

/* ===== أنماط المشتريات ===== */
.purchases-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.purchases-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.purchase-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.purchase-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.purchase-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.purchase-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.purchase-supplier,
.purchase-date {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

.purchase-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.purchase-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: 15px;
}

.purchase-summary .summary-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.purchase-summary .label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.purchase-summary .value {
    font-weight: 600;
    color: var(--text-primary);
}

.purchase-summary .total .value {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.purchase-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: var(--warning-color);
    color: white;
}

.status-badge.completed {
    background: var(--success-color);
    color: white;
}

.status-badge.cancelled {
    background: var(--error-color);
    color: white;
}

.payment-method {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 10px;
}

/* أنماط نموذج الشراء */
.purchase-items-section {
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: 15px;
}

.purchase-items-section h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
}

.items-header {
    margin-bottom: var(--spacing-lg);
}

.purchase-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.purchase-item {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 10px;
    box-shadow: var(--shadow-light);
}

.item-controls {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--spacing-md);
    align-items: center;
}

.item-controls select,
.item-controls input {
    padding: var(--spacing-sm);
    border: none;
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
}

.item-total {
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
}

.purchase-totals {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: 10px;
    box-shadow: var(--shadow-light);
}

.purchase-totals .total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.purchase-totals .total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    border-top: 2px solid var(--bg-primary);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    color: var(--primary-color);
}

/* تفاصيل فاتورة الشراء */
.purchase-details-view {
    max-width: 700px;
}

.purchase-header-info {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--bg-primary);
}

.purchase-header-info h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.purchase-meta p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

.purchase-items-details {
    margin-bottom: var(--spacing-xl);
}

.purchase-items-details h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
}

.items-table th,
.items-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--bg-secondary);
}

.items-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.items-table td {
    color: var(--text-secondary);
}

.purchase-totals-details {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: 15px;
    margin-bottom: var(--spacing-xl);
}

.purchase-totals-details .total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.purchase-totals-details .total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    border-top: 2px solid var(--bg-secondary);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    color: var(--primary-color);
}

.purchase-notes {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: 15px;
    margin-bottom: var(--spacing-xl);
}

.purchase-notes h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
}

.purchase-notes p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.purchase-actions-section {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* التصميم المتجاوب للمشتريات */
@media (max-width: 768px) {
    .purchases-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .purchase-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .purchase-actions {
        align-self: flex-end;
    }

    .purchase-summary {
        grid-template-columns: 1fr;
    }

    .item-controls {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .purchase-actions-section {
        flex-direction: column;
    }

    .items-table {
        font-size: var(--font-size-sm);
    }

    .items-table th,
    .items-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== أنماط الديون والمدفوعات ===== */
.header-actions {
    display: flex;
    gap: var(--spacing-md);
}

.debts-summary {
    margin-bottom: var(--spacing-2xl);
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.debt-summary-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition);
}

.debt-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.debt-summary-card.customers {
    border-right: 4px solid var(--error-color);
}

.debt-summary-card.suppliers {
    border-right: 4px solid var(--warning-color);
}

.debt-summary-card.payments {
    border-right: 4px solid var(--success-color);
}

.debt-summary-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.debt-summary-card.customers .card-icon {
    background: var(--error-color);
}

.debt-summary-card.suppliers .card-icon {
    background: var(--warning-color);
}

.debt-summary-card.payments .card-icon {
    background: var(--success-color);
}

.debt-summary-card .card-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.debt-summary-card .card-info p {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.debt-summary-card .card-info span {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.debts-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    background: var(--bg-secondary);
    padding: var(--spacing-sm);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.debts-tabs .tab-btn {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 10px;
    background: transparent;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.debts-tabs .tab-btn:hover {
    background: var(--bg-primary);
}

.debts-tabs .tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-light);
}

.debts-content {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.debts-tab {
    display: none;
}

.debts-tab.active {
    display: block;
}

.debts-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.debts-list,
.payments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.debt-item,
.payment-item {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-right: 4px solid;
}

.debt-item.customer-debt {
    border-color: var(--error-color);
}

.debt-item.supplier-debt {
    border-color: var(--warning-color);
}

.payment-item {
    border-color: var(--success-color);
}

.debt-item.overdue {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), var(--bg-primary));
}

.debt-item:hover,
.payment-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-hover);
}

.debt-info,
.payment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.debtor-details h4,
.payment-details h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.contact-info,
.payment-type,
.debt-age,
.payment-date {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.payment-notes {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: var(--spacing-xs) 0;
    font-style: italic;
}

.overdue-badge {
    background: var(--error-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.debt-amount,
.payment-amount {
    text-align: center;
}

.debt-amount .amount,
.payment-amount .amount {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.debt-amount .label,
.payment-amount .label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.debt-actions,
.payment-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.payment-info-display {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.payment-info-display .positive {
    color: var(--success-color);
    font-weight: 600;
}

.payment-info-display .negative {
    color: var(--error-color);
    font-weight: 600;
}

/* التصميم المتجاوب للديون */
@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .debts-tabs {
        flex-direction: column;
    }

    .debts-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .debt-info,
    .payment-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .debt-amount,
    .payment-amount {
        text-align: right;
        width: 100%;
    }

    .debt-actions,
    .payment-actions {
        justify-content: flex-start;
        flex-wrap: wrap;
    }
}

/* ===== أنماط التقارير ===== */
.reports-filters {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.filter-group input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
}

.filter-group:last-child {
    flex-direction: row;
    gap: var(--spacing-sm);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.report-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--bg-primary);
}

.report-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.report-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.stat-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.stat-value.profit.positive {
    color: var(--success-color);
}

.stat-value.profit.negative {
    color: var(--error-color);
}

.report-chart {
    background: var(--bg-primary);
    border-radius: 10px;
    padding: var(--spacing-md);
    height: 200px;
}

.report-chart canvas {
    width: 100%;
    height: 100%;
}

.low-stock-list {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
}

.low-stock-list h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.low-stock-list ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.low-stock-list li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    border-bottom: 1px solid var(--bg-secondary);
}

.low-stock-list li:last-child {
    border-bottom: none;
}

.success-message {
    color: var(--success-color);
    font-weight: 500;
    text-align: center;
    margin: 0;
}

/* تحسينات تقرير المخزون */
.low-stock-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.low-stock-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
    border-right: 3px solid var(--warning-color);
}

.low-stock-item .product-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.low-stock-item .total-qty {
    font-size: var(--font-size-sm);
    color: var(--warning-color);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.low-stock-item .warehouse-breakdown {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.more-items {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-md);
}

/* تقرير المخازن */
.warehouse-breakdown h4 {
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.warehouse-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.warehouse-detail-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 10px;
    border-right: 3px solid var(--info-color);
}

.warehouse-detail-item .warehouse-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.warehouse-stats-mini {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.warehouse-stats-mini span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* التصميم المتجاوب للتقارير */
@media (max-width: 768px) {
    .reports-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group:last-child {
        flex-direction: column;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .report-stats {
        grid-template-columns: 1fr;
    }
}

/* ===== أنماط المخازن ===== */
.warehouses-overview {
    margin-bottom: var(--spacing-2xl);
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.overview-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition);
}

.overview-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.overview-card.warning {
    border-right: 4px solid var(--warning-color);
}

.overview-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.overview-card.warning .card-icon {
    background: var(--warning-color);
}

.overview-card .card-info h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.overview-card .card-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.warehouses-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    background: var(--bg-secondary);
    padding: var(--spacing-sm);
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.warehouses-tabs .tab-btn {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 10px;
    background: transparent;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.warehouses-tabs .tab-btn:hover {
    background: var(--bg-primary);
}

.warehouses-tabs .tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-light);
}

.warehouses-content {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.warehouse-tab {
    display: none;
}

.warehouse-tab.active {
    display: block;
}

.warehouses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.warehouse-card {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-right: 4px solid;
}

.warehouse-card.active {
    border-color: var(--success-color);
}

.warehouse-card.inactive {
    border-color: var(--error-color);
    opacity: 0.7;
}

.warehouse-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.warehouse-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.warehouse-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.warehouse-location,
.warehouse-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: var(--spacing-xs) 0;
}

.warehouse-status .status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.warehouse-status .status-badge.active {
    background: var(--success-color);
    color: white;
}

.warehouse-status .status-badge.inactive {
    background: var(--error-color);
    color: white;
}

.warehouse-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.warehouse-stats .stat-item {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.warehouse-stats .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.warehouse-stats .stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

.warehouse-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* جدول المخزون */
.inventory-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.inventory-table-container {
    overflow-x: auto;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.inventory-table-container {
    overflow-x: auto;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.inventory-table {
    width: 100%;
    min-width: 800px;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 15px;
    overflow: hidden;
}

.inventory-table th,
.inventory-table td {
    padding: var(--spacing-md);
    text-align: center;
    border-bottom: 1px solid var(--bg-secondary);
    vertical-align: middle;
    white-space: nowrap;
}

.inventory-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
    border-right: 1px solid var(--border-color);
}

.inventory-table th:first-child,
.inventory-table td:first-child {
    text-align: right;
    min-width: 200px;
}

.inventory-table th:last-child,
.inventory-table td:last-child {
    border-right: none;
}

.inventory-table td {
    color: var(--text-secondary);
}

.inventory-table .product-name {
    font-weight: 600;
    color: var(--text-primary);
}

.inventory-table .warehouse-qty,
.inventory-table .total-qty,
.inventory-table .warehouse-header {
    text-align: center;
    font-weight: 600;
    min-width: 120px;
}

.inventory-table .warehouse-header {
    background: var(--primary-color);
    color: white;
}

.inventory-table .product-name-header,
.inventory-table .category-header {
    text-align: right;
    min-width: 150px;
}

.inventory-table .total-header,
.inventory-table .min-header,
.inventory-table .status-header {
    text-align: center;
    min-width: 100px;
}

.inventory-table .actions-header {
    text-align: center;
    min-width: 120px;
}

.inventory-table .stock-status {
    text-align: center;
    font-weight: 600;
}

.inventory-table .stock-status.in-stock {
    color: var(--success-color);
}

.inventory-table .stock-status.low-stock {
    color: var(--warning-color);
}

.inventory-table .stock-status.out-of-stock {
    color: var(--error-color);
}

.inventory-actions {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

/* حركات المخزون */
.movements-controls {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: center;
}

.movements-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.movement-item {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-right: 4px solid;
}

.movement-item.transfer {
    border-color: var(--info-color);
}

.movement-item.adjustment {
    border-color: var(--warning-color);
}

.movement-item.sale {
    border-color: var(--error-color);
}

.movement-item.purchase {
    border-color: var(--success-color);
}

.movement-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-hover);
}

/* التصميم المتجاوب للمخازن */
@media (max-width: 768px) {
    .overview-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .warehouses-tabs {
        flex-direction: column;
    }

    .warehouses-grid {
        grid-template-columns: 1fr;
    }

    .warehouse-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .warehouse-stats {
        grid-template-columns: 1fr;
    }

    .warehouse-actions {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .inventory-controls,
    .movements-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .inventory-table {
        font-size: var(--font-size-sm);
    }

    .inventory-table th,
    .inventory-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== أنماط استيراد المنتجات ===== */
.import-products-form {
    max-width: 600px;
}

.file-input-container {
    position: relative;
    margin-bottom: var(--spacing-sm);
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.file-input-label {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-tertiary);
    border: 2px dashed var(--border-color);
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    width: 100%;
    color: var(--text-secondary);
    font-weight: 500;
}

.file-input-label:hover {
    border-color: var(--primary-color);
    background: var(--bg-primary);
    color: var(--primary-color);
}

.file-input-label i {
    margin-left: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 8px;
    transition: var(--transition);
}

.checkbox-label:hover {
    background: var(--bg-tertiary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.import-progress {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 10px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.import-results {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 10px;
}

.import-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: 8px;
    font-weight: 500;
}

.summary-item.success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.summary-item.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.summary-item.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.summary-item.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.error-details {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: 8px;
    border-right: 4px solid var(--error-color);
}

.error-details h5 {
    color: var(--error-color);
    margin-bottom: var(--spacing-sm);
}

.error-details ul {
    margin: 0;
    padding-right: var(--spacing-lg);
    color: var(--text-secondary);
}

.error-details li {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

/* تحسينات للأزرار الجديدة */
.section-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.section-actions .btn {
    white-space: nowrap;
}

@media (max-width: 768px) {
    .section-actions {
        flex-direction: column;
        width: 100%;
    }

    .section-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .import-summary {
        grid-template-columns: 1fr;
    }

    .checkbox-group {
        gap: var(--spacing-md);
    }
}

/* ===== إدارة الفئات في الإعدادات ===== */
.category-management {
    margin-top: var(--spacing-lg);
}

.category-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.category-item {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-lg);
    transition: var(--transition);
}

.category-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.category-number {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.category-name {
    flex: 1;
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.category-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.category-info {
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.category-id {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
    font-family: 'Courier New', monospace;
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    display: inline-block;
}

.category-description {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.empty-state p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

/* نافذة إدارة الفئة */
#categoryModal .modal-content {
    max-width: 500px;
}

#categoryModal .form-group {
    margin-bottom: var(--spacing-lg);
}

#categoryModal .form-help {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    display: block;
}

#categoryModal textarea {
    resize: vertical;
    min-height: 80px;
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }

    .category-actions {
        justify-content: center;
    }

    .category-header {
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .category-name {
        order: 1;
        flex-basis: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .category-number {
        order: 2;
    }

    .category-actions {
        order: 3;
    }
}
