<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أبوسليمان للمحاسبة - نظام إدارة نقاط البيع</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-cash-register"></i>
                    <h1>أبوسليمان للمحاسبة</h1>
                    <p>نظام إدارة نقاط البيع</p>
                </div>
            </div>
            <form id="loginForm" class="login-form">
                <div class="input-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم" required autocomplete="username">
                    <i class="fas fa-user"></i>
                </div>

                <div class="input-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" placeholder="أدخل كلمة المرور" required autocomplete="current-password">
                    <i class="fas fa-lock"></i>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>

                <div id="loginError" class="login-error" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app hidden">
        <!-- شريط التنقل العلوي -->
        <header class="top-navbar">
            <div class="navbar-brand">
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()" style="display: none;">
                    <i class="fas fa-bars"></i>
                </button>
                <i class="fas fa-cash-register"></i>
                <span>أبوسليمان للمحاسبة</span>
            </div>
            <div class="navbar-controls">
                <button id="themeToggle" class="theme-toggle">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <button class="user-btn">
                        <i class="fas fa-user"></i>
                        <span class="user-name">المدير</span>
                    </button>
                    <div class="dropdown-menu">
                        <a href="#" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Overlay للجوال -->
        <div class="mobile-overlay" onclick="closeMobileMenu()"></div>

        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#" onclick="showSection('dashboard')" class="nav-link active">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة المعلومات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('sales')" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>المبيعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('products')" class="nav-link">
                            <i class="fas fa-box"></i>
                            <span>المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('customers')" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('suppliers')" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('purchases')" class="nav-link">
                            <i class="fas fa-shopping-bag"></i>
                            <span>المشتريات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('warehouses')" class="nav-link">
                            <i class="fas fa-warehouse"></i>
                            <span>المخازن</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('debts')" class="nav-link">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الديون والمدفوعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('reports')" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('settings')" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- لوحة المعلومات -->
            <section id="dashboard" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h2>
                    <div class="date-time-info">
                        <div class="date-info">
                            <div class="hijri-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span id="currentHijriDate"></span>
                            </div>
                            <div class="gregorian-date">
                                <i class="fas fa-calendar"></i>
                                <span id="currentGregorianDate"></span>
                            </div>
                        </div>
                        <div class="time-info">
                            <div class="current-time">
                                <i class="fas fa-clock"></i>
                                <span id="currentTime"></span>
                            </div>
                            <div class="day-of-week">
                                <span id="dayOfWeek"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card clickable-card" onclick="goToSales()">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalSales">٠.٠٠٠ د.ك</h3>
                            <p>إجمالي المبيعات اليوم</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>
                    
                    <div class="stat-card clickable-card" onclick="goToProducts()">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalProducts">٠</h3>
                            <p>عدد المنتجات</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <div class="stat-card clickable-card" onclick="goToCustomers()">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCustomers">٠</h3>
                            <p>عدد العملاء</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>
                    
                    <div class="stat-card clickable-card" onclick="showLowStockDetails()">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="lowStockItems">٠</h3>
                            <p>منتجات منخفضة المخزون</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <!-- بطاقات الفواتير الجديدة -->
                    <div class="stat-card clickable-card" onclick="goToSalesHistory()">
                        <div class="stat-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalInvoices">٠</h3>
                            <p>إجمالي الفواتير</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <div class="stat-card clickable-card" onclick="goToSalesHistory()">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="todayInvoices">٠</h3>
                            <p>فواتير اليوم</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <div class="stat-card clickable-card" onclick="goToSalesHistory()">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="weekInvoices">٠</h3>
                            <p>فواتير هذا الأسبوع</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <div class="stat-card clickable-card" onclick="goToSalesHistory()">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="monthInvoices">٠</h3>
                            <p>فواتير هذا الشهر</p>
                        </div>
                        <div class="card-action-hint">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>
                </div>

                <!-- لوحة الاختصارات السريعة -->
                <div class="quick-actions-section">
                    <div class="section-title">
                        <h3><i class="fas fa-bolt"></i> الاختصارات السريعة</h3>
                        <p>الوصول السريع للوظائف الأكثر استخداماً</p>
                    </div>

                    <div class="quick-actions-grid">
                        <!-- مجموعة المبيعات -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-shopping-cart"></i>
                                <span>المبيعات</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showSection('sales')">
                                    <i class="fas fa-cash-register"></i>
                                    <span>نقطة البيع</span>
                                </button>
                                <button class="action-btn" onclick="showAddInvoiceModal()">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>فاتورة جديدة</span>
                                </button>
                                <button class="action-btn" onclick="showRecentTransactions()">
                                    <i class="fas fa-history"></i>
                                    <span>المعاملات الأخيرة</span>
                                </button>
                            </div>
                        </div>

                        <!-- مجموعة المنتجات -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-box"></i>
                                <span>المنتجات</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showAddProductModal()">
                                    <i class="fas fa-plus"></i>
                                    <span>إضافة منتج</span>
                                </button>
                                <button class="action-btn warning" onclick="showLowStockDetails()">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>تنبيهات المخزون</span>
                                </button>
                                <button class="action-btn" onclick="showSection('products')">
                                    <i class="fas fa-warehouse"></i>
                                    <span>إدارة المخزون</span>
                                </button>
                            </div>
                        </div>

                        <!-- مجموعة العملاء -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-users"></i>
                                <span>العملاء</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showAddCustomerModal()">
                                    <i class="fas fa-user-plus"></i>
                                    <span>إضافة عميل</span>
                                </button>
                                <button class="action-btn" onclick="showPaymentCollection()">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>تحصيل مدفوعات</span>
                                </button>
                                <button class="action-btn" onclick="showCustomerStatements()">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>كشوف الحسابات</span>
                                </button>
                            </div>
                        </div>

                        <!-- مجموعة الموردين -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-truck"></i>
                                <span>الموردين</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showAddSupplierModal()">
                                    <i class="fas fa-plus"></i>
                                    <span>إضافة مورد</span>
                                </button>
                                <button class="action-btn" onclick="showAddPurchaseModal()">
                                    <i class="fas fa-shopping-bag"></i>
                                    <span>أمر شراء</span>
                                </button>
                                <button class="action-btn" onclick="showSupplierPayments()">
                                    <i class="fas fa-credit-card"></i>
                                    <span>إدارة المدفوعات</span>
                                </button>
                            </div>
                        </div>

                        <!-- مجموعة المخازن -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-warehouse"></i>
                                <span>المخازن</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showStockTransfer()">
                                    <i class="fas fa-exchange-alt"></i>
                                    <span>نقل مخزون</span>
                                </button>
                                <button class="action-btn" onclick="showInventoryAdjustment()">
                                    <i class="fas fa-edit"></i>
                                    <span>تعديل الجرد</span>
                                </button>
                                <button class="action-btn" onclick="showMovementHistory()">
                                    <i class="fas fa-list-alt"></i>
                                    <span>سجل الحركات</span>
                                </button>
                            </div>
                        </div>

                        <!-- مجموعة التقارير -->
                        <div class="action-group">
                            <div class="group-header">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </div>
                            <div class="action-buttons">
                                <button class="action-btn primary" onclick="showDailySalesReport()">
                                    <i class="fas fa-calendar-day"></i>
                                    <span>مبيعات اليوم</span>
                                </button>
                                <button class="action-btn" onclick="showInventoryReport()">
                                    <i class="fas fa-boxes"></i>
                                    <span>تقرير المخزون</span>
                                </button>
                                <button class="action-btn" onclick="showFinancialSummary()">
                                    <i class="fas fa-calculator"></i>
                                    <span>الملخص المالي</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتنبيهات -->
                <div class="dashboard-grid">
                    <div class="chart-container">
                        <h3>مبيعات الأسبوع</h3>
                        <canvas id="salesChart"></canvas>
                    </div>
                    
                    <div class="alerts-container">
                        <h3>التنبيهات</h3>
                        <div id="alertsList" class="alerts-list">
                            <!-- التنبيهات ستظهر هنا -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- أقسام أخرى ستتم إضافتها -->
            <section id="sales" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-shopping-cart"></i> نقطة البيع</h2>
                    <div class="section-actions">
                        <button class="btn btn-success" onclick="newSale()">
                            <i class="fas fa-plus"></i>
                            بيع جديد
                        </button>
                    </div>
                </div>

                <div class="pos-container">
                    <!-- قسم معلومات الفاتورة -->
                    <div class="invoice-info-section">
                        <div class="invoice-info-grid">
                            <div class="invoice-field">
                                <label for="warehouseSelect">المخزن:</label>
                                <select id="warehouseSelect" onchange="onWarehouseChange()">
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>

                            <div class="invoice-field">
                                <label for="invoiceDate">تاريخ الفاتورة:</label>
                                <input type="date" id="invoiceDate" value="">
                            </div>

                            <div class="invoice-field">
                                <label>رقم الفاتورة:</label>
                                <div class="invoice-number-display" id="invoiceNumberDisplay">
                                    <span class="invoice-prefix">ABUSLEAN-SALE-</span>
                                    <span class="invoice-number" id="currentInvoiceNumber">--</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pos-layout">
                        <!-- قسم المنتجات -->
                        <div class="products-section">
                            <div class="products-header">
                                <h3>المنتجات</h3>
                                <div class="products-filters">
                                    <input type="text" id="productSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
                                    <select id="categoryFilter" onchange="filterByCategory()">
                                        <option value="">جميع الفئات</option>
                                    </select>
                                </div>
                            </div>
                            <div id="productsGrid" class="products-grid">
                                <div class="loading">جاري التحميل...</div>
                            </div>
                        </div>

                        <!-- قسم السلة -->
                        <div class="cart-section">
                            <div class="cart-header">
                                <h3>السلة</h3>
                                <button class="btn btn-sm btn-secondary" onclick="clearCart()">
                                    <i class="fas fa-trash"></i>
                                    مسح الكل
                                </button>
                            </div>

                            <div id="cartItems" class="cart-items">
                                <!-- عناصر السلة ستظهر هنا -->
                            </div>

                            <div class="cart-summary">
                                <div class="summary-row">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0.000 د.ك</span>
                                </div>

                                <div class="summary-row">
                                    <span>الخصم:</span>
                                    <div class="discount-input">
                                        <input type="number" id="discountAmount" value="0" step="0.001" min="0" onchange="updateTotals()">
                                        <select id="discountType" onchange="updateTotals()">
                                            <option value="amount">د.ك</option>
                                            <option value="percentage">%</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="summary-row">
                                    <span>الضريبة:</span>
                                    <div class="tax-input">
                                        <input type="number" id="taxAmount" value="0" step="0.001" min="0" onchange="updateTotals()">
                                        <select id="taxType" onchange="updateTotals()">
                                            <option value="amount">د.ك</option>
                                            <option value="percentage">%</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="summary-row total-row">
                                    <span>الإجمالي:</span>
                                    <span id="total">0.000 د.ك</span>
                                </div>
                            </div>

                            <div class="cart-actions">
                                <div class="customer-selection">
                                    <label for="customerSelect">العميل:</label>
                                    <select id="customerSelect">
                                        <option value="">اختر عميل</option>
                                    </select>
                                </div>

                                <div class="payment-methods">
                                    <label>طريقة الدفع:</label>
                                    <div class="payment-options">
                                        <label class="radio-label">
                                            <input type="radio" name="paymentMethod" value="cash" checked>
                                            <span>نقدي</span>
                                        </label>
                                        <label class="radio-label">
                                            <input type="radio" name="paymentMethod" value="card">
                                            <span>بطاقة</span>
                                        </label>
                                        <label class="radio-label">
                                            <input type="radio" name="paymentMethod" value="credit">
                                            <span>آجل</span>
                                        </label>
                                    </div>
                                </div>

                                <button class="btn btn-primary btn-large" onclick="completeSale()" id="completeSaleBtn" disabled>
                                    <i class="fas fa-check"></i>
                                    إتمام البيع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="products" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-box"></i> المنتجات</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>

            <section id="customers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> العملاء</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>

            <section id="suppliers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-truck"></i> الموردين</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>

            <section id="purchases" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-shopping-bag"></i> المشتريات</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="showAddPurchaseModal()">
                            <i class="fas fa-plus"></i>
                            إضافة فاتورة شراء
                        </button>
                    </div>
                </div>
                <div id="purchasesContainer" class="purchases-grid">
                    <div class="loading">جاري التحميل...</div>
                </div>
            </section>

            <section id="warehouses" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-warehouse"></i> إدارة المخازن</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>

            <section id="debts" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-money-bill-wave"></i> الديون والمدفوعات</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>

            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> التقارير</h2>
                </div>

                <div class="reports-container">
                    <!-- تبويبات التقارير -->
                    <div class="reports-tabs">
                        <button class="tab-btn active" onclick="showReportTab('sales')">
                            <i class="fas fa-shopping-cart"></i>
                            تقارير المبيعات
                        </button>
                        <button class="tab-btn" onclick="showReportTab('customers')">
                            <i class="fas fa-users"></i>
                            تقارير العملاء
                        </button>
                        <button class="tab-btn" onclick="showReportTab('suppliers')">
                            <i class="fas fa-truck"></i>
                            تقارير الموردين
                        </button>
                        <button class="tab-btn" onclick="showReportTab('inventory')">
                            <i class="fas fa-boxes"></i>
                            تقارير المخزون
                        </button>
                    </div>

                    <!-- محتوى التقارير -->
                    <div class="reports-content">
                        <!-- تقارير المبيعات -->
                        <div id="salesReports" class="report-tab-content active">
                            <div class="report-section">
                                <div class="report-header">
                                    <h3>تقارير المبيعات</h3>
                                    <div class="report-actions">
                                        <button class="btn btn-secondary" onclick="clearSalesFilters()">
                                            <i class="fas fa-eraser"></i>
                                            مسح الفلاتر
                                        </button>
                                        <button class="btn btn-primary" onclick="exportSalesReport()">
                                            <i class="fas fa-download"></i>
                                            تصدير التقرير
                                        </button>
                                    </div>
                                </div>

                                <div class="filters-panel">
                                    <div class="filters-grid">
                                        <div class="filter-group">
                                            <label for="salesProductFilter">المنتج:</label>
                                            <select id="salesProductFilter" onchange="applySalesFilters()">
                                                <option value="">جميع المنتجات</option>
                                            </select>
                                        </div>

                                        <div class="filter-group">
                                            <label for="salesFromDate">من تاريخ:</label>
                                            <input type="date" id="salesFromDate" onchange="applySalesFilters()">
                                        </div>

                                        <div class="filter-group">
                                            <label for="salesToDate">إلى تاريخ:</label>
                                            <input type="date" id="salesToDate" onchange="applySalesFilters()">
                                        </div>

                                        <div class="filter-group">
                                            <label for="salesCustomerFilter">العميل:</label>
                                            <select id="salesCustomerFilter" onchange="applySalesFilters()">
                                                <option value="">جميع العملاء</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-summary" id="salesSummary">
                                    <!-- ملخص المبيعات -->
                                </div>

                                <div class="report-table-container">
                                    <div class="loading-indicator" id="salesLoading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل التقرير...
                                    </div>
                                    <div id="salesReportTable">
                                        <!-- جدول تقرير المبيعات -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير العملاء -->
                        <div id="customersReports" class="report-tab-content">
                            <div class="report-section">
                                <div class="report-header">
                                    <h3>تقارير العملاء</h3>
                                    <div class="report-actions">
                                        <button class="btn btn-secondary" onclick="clearCustomersFilters()">
                                            <i class="fas fa-eraser"></i>
                                            مسح الفلاتر
                                        </button>
                                        <button class="btn btn-primary" onclick="exportCustomersReport()">
                                            <i class="fas fa-download"></i>
                                            تصدير التقرير
                                        </button>
                                    </div>
                                </div>

                                <div class="filters-panel">
                                    <div class="filters-grid">
                                        <div class="filter-group">
                                            <label for="customersProductFilter">المنتج:</label>
                                            <select id="customersProductFilter" onchange="applyCustomersFilters()">
                                                <option value="">جميع المنتجات</option>
                                            </select>
                                        </div>

                                        <div class="filter-group">
                                            <label for="customersFromDate">من تاريخ:</label>
                                            <input type="date" id="customersFromDate" onchange="applyCustomersFilters()">
                                        </div>

                                        <div class="filter-group">
                                            <label for="customersToDate">إلى تاريخ:</label>
                                            <input type="date" id="customersToDate" onchange="applyCustomersFilters()">
                                        </div>
                                    </div>
                                </div>

                                <div class="report-summary" id="customersSummary">
                                    <!-- ملخص العملاء -->
                                </div>

                                <div class="report-table-container">
                                    <div class="loading-indicator" id="customersLoading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل التقرير...
                                    </div>
                                    <div id="customersReportTable">
                                        <!-- جدول تقرير العملاء -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير الموردين -->
                        <div id="suppliersReports" class="report-tab-content">
                            <div class="report-section">
                                <div class="report-header">
                                    <h3>تقارير الموردين</h3>
                                    <div class="report-actions">
                                        <button class="btn btn-secondary" onclick="clearSuppliersFilters()">
                                            <i class="fas fa-eraser"></i>
                                            مسح الفلاتر
                                        </button>
                                        <button class="btn btn-primary" onclick="exportSuppliersReport()">
                                            <i class="fas fa-download"></i>
                                            تصدير التقرير
                                        </button>
                                    </div>
                                </div>

                                <div class="filters-panel">
                                    <div class="filters-grid">
                                        <div class="filter-group">
                                            <label for="suppliersFromDate">من تاريخ:</label>
                                            <input type="date" id="suppliersFromDate" onchange="applySuppliersFilters()">
                                        </div>

                                        <div class="filter-group">
                                            <label for="suppliersToDate">إلى تاريخ:</label>
                                            <input type="date" id="suppliersToDate" onchange="applySuppliersFilters()">
                                        </div>

                                        <div class="filter-group">
                                            <label for="suppliersProductFilter">المنتج:</label>
                                            <select id="suppliersProductFilter" onchange="applySuppliersFilters()">
                                                <option value="">جميع المنتجات</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-summary" id="suppliersSummary">
                                    <!-- ملخص الموردين -->
                                </div>

                                <div class="report-table-container">
                                    <div class="loading-indicator" id="suppliersLoading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل التقرير...
                                    </div>
                                    <div id="suppliersReportTable">
                                        <!-- جدول تقرير الموردين -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير المخزون -->
                        <div id="inventoryReports" class="report-tab-content">
                            <div class="report-section">
                                <div class="report-header">
                                    <h3>تقارير المخزون</h3>
                                    <div class="report-actions">
                                        <button class="btn btn-secondary" onclick="clearInventoryFilters()">
                                            <i class="fas fa-eraser"></i>
                                            مسح الفلاتر
                                        </button>
                                        <button class="btn btn-primary" onclick="exportInventoryReport()">
                                            <i class="fas fa-download"></i>
                                            تصدير التقرير
                                        </button>
                                    </div>
                                </div>

                                <div class="filters-panel">
                                    <div class="filters-grid">
                                        <div class="filter-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="warehouseStockCheckbox" onchange="applyInventoryFilters()">
                                                <span class="checkmark"></span>
                                                عرض توزيع المخازن
                                            </label>
                                        </div>

                                        <div class="filter-group">
                                            <label for="inventoryWarehouseFilter">المخزن:</label>
                                            <select id="inventoryWarehouseFilter" onchange="applyInventoryFilters()">
                                                <option value="">جميع المخازن</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-summary" id="inventorySummary">
                                    <!-- ملخص المخزون -->
                                </div>

                                <div class="report-table-container">
                                    <div class="loading-indicator" id="inventoryLoading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل التقرير...
                                    </div>
                                    <div id="inventoryReportTable">
                                        <!-- جدول تقرير المخزون -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="settings" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                </div>
                <div class="loading">جاري التحميل...</div>
            </section>
        </main>
    </div>

    <!-- النوافذ المنبثقة -->
    <div id="modalOverlay" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">عنوان النافذة</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- محتوى النافذة -->
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد البيع -->
    <div id="saleConfirmModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد البيع</h3>
                <button class="modal-close" onclick="closeSaleConfirm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="saleConfirmContent">
                    <!-- محتوى تأكيد البيع -->
                </div>
                <div class="modal-actions">
                    <button class="btn btn-primary" onclick="confirmSale()">
                        <i class="fas fa-check"></i>
                        تأكيد البيع
                    </button>
                    <button class="btn btn-secondary" onclick="closeSaleConfirm()">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="database.js"></script>
    <script src="sample-data.js"></script>
    <script src="main.js"></script>
    <script src="app.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/categories.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/reports.js"></script>


</body>
</html>
