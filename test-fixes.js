/**
 * اختبار شامل للتحقق من إصلاح جميع الأخطاء
 * أبوسليمان للمحاسبة - نظام إدارة نقاط البيع
 */

// اختبار شامل لجميع الإصلاحات
function runComprehensiveFixValidation() {
    console.log('🧪 بدء اختبار شامل للتحقق من إصلاح جميع الأخطاء...');
    
    let totalTests = 0;
    let passedTests = 0;
    const testResults = [];

    try {
        // اختبار 1: التحقق من عدم وجود أخطاء في تعريف المتغيرات
        totalTests++;
        console.log('\n📋 اختبار 1: التحقق من تعريف المتغيرات...');
        
        try {
            // التحقق من متغيرات المبيعات
            if (typeof window.cart !== 'undefined' && typeof cart !== 'undefined') {
                passedTests++;
                testResults.push('✅ متغير cart: تم تعريفه بنجاح');
            } else {
                testResults.push('❌ متغير cart: غير معرف');
            }

            // التحقق من متغيرات المشتريات
            if (typeof window.purchaseItems !== 'undefined' && typeof purchaseItems !== 'undefined') {
                passedTests++;
                testResults.push('✅ متغير purchaseItems: تم تعريفه بنجاح');
            } else {
                testResults.push('❌ متغير purchaseItems: غير معرف');
            }
        } catch (error) {
            testResults.push('❌ خطأ في تعريف المتغيرات: ' + error.message);
        }

        // اختبار 2: التحقق من وجود دالة toArabicNumbers
        totalTests++;
        console.log('\n🔢 اختبار 2: التحقق من دالة toArabicNumbers...');
        
        try {
            if (window.db && typeof window.db.toArabicNumbers === 'function') {
                const testNumber = window.db.toArabicNumbers('123');
                if (testNumber) {
                    passedTests++;
                    testResults.push('✅ دالة toArabicNumbers: تعمل بنجاح');
                } else {
                    testResults.push('❌ دالة toArabicNumbers: لا تعيد نتيجة');
                }
            } else {
                testResults.push('❌ دالة toArabicNumbers: غير موجودة');
            }
        } catch (error) {
            testResults.push('❌ خطأ في دالة toArabicNumbers: ' + error.message);
        }

        // اختبار 3: التحقق من وجود ملف sample-data.js
        totalTests++;
        console.log('\n📁 اختبار 3: التحقق من ملف sample-data.js...');
        
        try {
            if (typeof loadSampleData === 'function' && typeof createDefaultAdmin === 'function') {
                passedTests++;
                testResults.push('✅ ملف sample-data.js: تم تحميله بنجاح');
            } else {
                testResults.push('❌ ملف sample-data.js: غير محمل أو ناقص');
            }
        } catch (error) {
            testResults.push('❌ خطأ في ملف sample-data.js: ' + error.message);
        }

        // اختبار 4: التحقق من قاعدة البيانات
        totalTests++;
        console.log('\n💾 اختبار 4: التحقق من قاعدة البيانات...');
        
        try {
            if (window.db && typeof window.db.deleteRecord === 'function') {
                passedTests++;
                testResults.push('✅ قاعدة البيانات: دالة deleteRecord موجودة');
            } else {
                testResults.push('❌ قاعدة البيانات: دالة deleteRecord مفقودة');
            }
        } catch (error) {
            testResults.push('❌ خطأ في قاعدة البيانات: ' + error.message);
        }

        // اختبار 5: التحقق من وظائف المخزون
        totalTests++;
        console.log('\n📦 اختبار 5: التحقق من وظائف المخزون...');
        
        try {
            const inventoryFunctions = [
                'updateInventoryAfterSale',
                'updateInventoryAfterPurchase',
                'reverseSaleInventoryChanges',
                'reversePurchaseInventoryChanges'
            ];

            let inventoryFunctionsFound = 0;
            inventoryFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    inventoryFunctionsFound++;
                }
            });

            if (inventoryFunctionsFound === inventoryFunctions.length) {
                passedTests++;
                testResults.push('✅ وظائف المخزون: جميع الوظائف موجودة');
            } else {
                testResults.push(`❌ وظائف المخزون: ${inventoryFunctionsFound}/${inventoryFunctions.length} موجودة`);
            }
        } catch (error) {
            testResults.push('❌ خطأ في وظائف المخزون: ' + error.message);
        }

        // اختبار 6: التحقق من وظائف الحذف
        totalTests++;
        console.log('\n🗑️ اختبار 6: التحقق من وظائف الحذف...');
        
        try {
            const deleteFunctions = ['deletePurchase', 'deleteSale'];
            let deleteFunctionsFound = 0;
            
            deleteFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    deleteFunctionsFound++;
                }
            });

            if (deleteFunctionsFound === deleteFunctions.length) {
                passedTests++;
                testResults.push('✅ وظائف الحذف: جميع الوظائف موجودة');
            } else {
                testResults.push(`❌ وظائف الحذف: ${deleteFunctionsFound}/${deleteFunctions.length} موجودة`);
            }
        } catch (error) {
            testResults.push('❌ خطأ في وظائف الحذف: ' + error.message);
        }

        // اختبار 7: اختبار تزامن المخزون
        totalTests++;
        console.log('\n🔄 اختبار 7: اختبار تزامن المخزون...');
        
        try {
            if (typeof testCompleteInventorySync === 'function') {
                passedTests++;
                testResults.push('✅ اختبار تزامن المخزون: الوظيفة موجودة');
            } else {
                testResults.push('❌ اختبار تزامن المخزون: الوظيفة مفقودة');
            }
        } catch (error) {
            testResults.push('❌ خطأ في اختبار تزامن المخزون: ' + error.message);
        }

        // عرض النتائج
        console.log('\n📊 نتائج الاختبار:');
        console.log(`إجمالي الاختبارات: ${totalTests}`);
        console.log(`الاختبارات الناجحة: ${passedTests}`);
        console.log(`معدل النجاح: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        console.log('\n📋 تفاصيل النتائج:');
        testResults.forEach(result => console.log(result));

        if (passedTests === totalTests) {
            console.log('\n🎉 جميع الاختبارات نجحت! تم إصلاح جميع الأخطاء بنجاح.');
            if (typeof showNotification === 'function') {
                showNotification('تم إصلاح جميع الأخطاء بنجاح!', 'success');
            }
            return true;
        } else {
            console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
            if (typeof showNotification === 'function') {
                showNotification(`${passedTests}/${totalTests} اختبارات نجحت`, 'warning');
            }
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تشغيل الاختبارات:', error);
        if (typeof showNotification === 'function') {
            showNotification('خطأ في تشغيل الاختبارات', 'error');
        }
        return false;
    }
}

// تشغيل الاختبار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار قليل للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        runComprehensiveFixValidation();
    }, 2000);
});

// تصدير الوظيفة للاستخدام العام
window.runComprehensiveFixValidation = runComprehensiveFixValidation;
