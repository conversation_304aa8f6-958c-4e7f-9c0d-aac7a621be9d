/**
 * بيانات تجريبية للنظام
 * أبوسليمان للمحاسبة - نظام إدارة نقاط البيع
 */

// تحميل البيانات التجريبية
function loadSampleData() {
    try {
        // التحقق من وجود قاعدة البيانات
        if (!window.db) {
            console.warn('قاعدة البيانات غير متاحة لتحميل البيانات التجريبية');
            return;
        }

        // التحقق من وجود بيانات موجودة
        const existingProducts = db.getTable('products');
        const existingCustomers = db.getTable('customers');
        const existingSuppliers = db.getTable('suppliers');

        // إذا كانت هناك بيانات موجودة، لا تحمل البيانات التجريبية
        if (existingProducts.length > 0 || existingCustomers.length > 1 || existingSuppliers.length > 0) {
            console.log('توجد بيانات موجودة، تم تخطي تحميل البيانات التجريبية');
            return;
        }

        console.log('تحميل البيانات التجريبية...');

        // إضافة منتجات تجريبية
        loadSampleProducts();

        // إضافة عملاء تجريبيين
        loadSampleCustomers();

        // إضافة موردين تجريبيين
        loadSampleSuppliers();

        console.log('✅ تم تحميل البيانات التجريبية بنجاح');

    } catch (error) {
        console.error('خطأ في تحميل البيانات التجريبية:', error);
    }
}

// تحميل منتجات تجريبية
function loadSampleProducts() {
    const sampleProducts = [
        {
            id: 'sample_product_1',
            name: 'منتج تجريبي 1',
            description: 'وصف المنتج التجريبي الأول',
            categoryId: 'electronics',
            purchasePrice: 10.000,
            salePrice: 15.000,
            quantity: 50,
            minStock: 5,
            warehouseDistribution: {
                'main': 30,
                'branch1': 15,
                'branch2': 5
            },
            createdAt: new Date().toISOString()
        },
        {
            id: 'sample_product_2',
            name: 'منتج تجريبي 2',
            description: 'وصف المنتج التجريبي الثاني',
            categoryId: 'clothing',
            purchasePrice: 25.000,
            salePrice: 40.000,
            quantity: 30,
            minStock: 3,
            warehouseDistribution: {
                'main': 20,
                'branch1': 7,
                'branch2': 3
            },
            createdAt: new Date().toISOString()
        }
    ];

    const products = db.getTable('products');
    sampleProducts.forEach(product => {
        products.push(product);
    });
    db.setTable('products', products);
}

// تحميل عملاء تجريبيين
function loadSampleCustomers() {
    const sampleCustomers = [
        {
            id: 'sample_customer_1',
            name: 'عميل تجريبي 1',
            phone: '+965 1234 5678',
            email: '<EMAIL>',
            address: 'الكويت - حولي',
            balance: 0,
            createdAt: new Date().toISOString()
        },
        {
            id: 'sample_customer_2',
            name: 'عميل تجريبي 2',
            phone: '+965 8765 4321',
            email: '<EMAIL>',
            address: 'الكويت - السالمية',
            balance: 0,
            createdAt: new Date().toISOString()
        }
    ];

    const customers = db.getTable('customers');
    sampleCustomers.forEach(customer => {
        customers.push(customer);
    });
    db.setTable('customers', customers);
}

// تحميل موردين تجريبيين
function loadSampleSuppliers() {
    const sampleSuppliers = [
        {
            id: 'sample_supplier_1',
            name: 'مورد تجريبي 1',
            phone: '+965 5555 1111',
            email: '<EMAIL>',
            address: 'الكويت - الفروانية',
            balance: 0,
            createdAt: new Date().toISOString()
        },
        {
            id: 'sample_supplier_2',
            name: 'مورد تجريبي 2',
            phone: '+965 5555 2222',
            email: '<EMAIL>',
            address: 'الكويت - الجهراء',
            balance: 0,
            createdAt: new Date().toISOString()
        }
    ];

    const suppliers = db.getTable('suppliers');
    sampleSuppliers.forEach(supplier => {
        suppliers.push(supplier);
    });
    db.setTable('suppliers', suppliers);
}

// إنشاء مستخدم افتراضي
function createDefaultAdmin() {
    try {
        if (!window.db) return;

        const users = db.getTable('users');
        
        // التحقق من وجود مستخدم افتراضي
        const adminExists = users.find(user => user.username === 'admin');
        
        if (!adminExists) {
            const defaultAdmin = {
                id: 'admin_user',
                username: 'admin',
                password: db.hashPassword('123'),
                role: 'admin',
                name: 'المدير',
                email: '<EMAIL>',
                createdAt: new Date().toISOString(),
                isActive: true
            };

            users.push(defaultAdmin);
            db.setTable('users', users);
            
            console.log('✅ تم إنشاء المستخدم الافتراضي');
        }

    } catch (error) {
        console.error('خطأ في إنشاء المستخدم الافتراضي:', error);
    }
}

// تصدير الوظائف للاستخدام العام
window.loadSampleData = loadSampleData;
window.createDefaultAdmin = createDefaultAdmin;
